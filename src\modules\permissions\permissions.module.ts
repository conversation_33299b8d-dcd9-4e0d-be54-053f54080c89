import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Role, PermissionEntity, UserRole } from "./models/entities";
import { RoleTypeOrmRepository, PermissionTypeOrmRepository, UserRoleTypeOrmRepository } from "./repositories";
import { CreateRoleUseCase, AssignRoleUseCase, ListRolesUseCase, GetUserPermissionsUseCase, SeedPermissionsUseCase } from "./use-cases";
import { PermissionsController } from "./controllers/permissions.controller";
import { RolesGuard, PermissionsGuard } from "./models/guards";
import { UserModule } from "../user/user.module";

@Module({
	imports: [
		TypeOrmModule.forFeature([Role, PermissionEntity, UserRole]),
		forwardRef(() => UserModule), // Para evitar dependência circular
	],
	controllers: [PermissionsController],
	providers: [
		// Repositórios
		RoleTypeOrmRepository,
		PermissionTypeOrmRepository,
		UserRoleTypeOrmRepository,

		// Use Cases
		CreateRoleUseCase,
		AssignRoleUseCase,
		ListRolesUseCase,
		GetUserPermissionsUseCase,
		SeedPermissionsUseCase,

		// Guards
		RolesGuard,
		PermissionsGuard,

		// Providers de interface
		{
			provide: "IRoleRepository",
			useExisting: RoleTypeOrmRepository,
		},
		{
			provide: "IPermissionRepository",
			useExisting: PermissionTypeOrmRepository,
		},
		{
			provide: "IUserRoleRepository",
			useExisting: UserRoleTypeOrmRepository,
		},
	],
	exports: [
		// Repositórios
		"IRoleRepository",
		"IPermissionRepository",
		"IUserRoleRepository",

		// Use Cases
		CreateRoleUseCase,
		AssignRoleUseCase,
		ListRolesUseCase,
		GetUserPermissionsUseCase,
		SeedPermissionsUseCase,

		// Guards
		RolesGuard,
		PermissionsGuard,
	],
})
export class PermissionsModule {}
