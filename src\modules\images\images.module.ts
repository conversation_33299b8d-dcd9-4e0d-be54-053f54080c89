import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { MulterModule } from "@nestjs/platform-express";
import { ScheduleModule } from "@nestjs/schedule";
import { PermissionsModule } from "../permissions/permissions.module";

// Entities
import { Image } from "./models/entities/image.entity";

// Controllers
import { ImagesController } from "./images.controller";

// Repositories
import { ImageRepository } from "./repositories/image.repository";

// Use Cases
import { UploadImageUseCase } from "./use-cases/upload-image/upload-image.use-case";
import { GetImageUseCase, ListImagesUseCase } from "./use-cases/get-image/get-image.use-case";
import { DeleteImageUseCase, RestoreImageUseCase } from "./use-cases/delete-image/delete-image.use-case";
import { CleanupTempUseCase, CleanupStatsUseCase, FullCleanupUseCase } from "./use-cases/cleanup-temp/cleanup-temp.use-case";

// Middlewares
import { MulterConfigService } from "./middlewares/multer-config.middleware";

/**
 * Módulo de gerenciamento de imagens
 * Seguindo princípios SOLID - Dependency Inversion
 */
@Module({
	imports: [
		TypeOrmModule.forFeature([Image]),
		MulterModule.registerAsync({
			useClass: MulterConfigService,
		}),
		ScheduleModule.forRoot(),
		PermissionsModule,
	],
	controllers: [ImagesController],
	providers: [
		// Repositories
		{
			provide: "IImageRepository",
			useClass: ImageRepository,
		},

		// // Services
		// {
		// 	provide: "IImageValidationService",
		// 	useClass: ImageValidationService,
		// },
		// {
		// 	provide: "IImageProcessingService",
		// 	useClass: ImageProcessingService,
		// },
		// {
		// 	provide: "IImageFileService",
		// 	useClass: ImageFileService,
		// },
		// {
		// 	provide: "IImageCleanupService",
		// 	useClass: ImageCleanupService,
		// },

		// Use Cases
		UploadImageUseCase,
		GetImageUseCase,
		ListImagesUseCase,
		DeleteImageUseCase,
		RestoreImageUseCase,
		CleanupTempUseCase,
		CleanupStatsUseCase,
		FullCleanupUseCase,

		// Middlewares/Config
		MulterConfigService,
	],
	exports: [
		"IImageRepository",
		// "IImageValidationService",
		// "IImageProcessingService",
		// "IImageFileService",
		// "IImageCleanupService",
		UploadImageUseCase,
		GetImageUseCase,
		ListImagesUseCase,
		DeleteImageUseCase,
		RestoreImageUseCase,
	],
})
export class ImagesModule {}
