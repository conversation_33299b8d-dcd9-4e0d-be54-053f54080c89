import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { CommentReaction } from "../models/entities/comment-reaction.entity";
import { ICommentReaction, ICommentReactionRepository } from "../models/interfaces";

@Injectable()
export class CommentReactionRepository implements ICommentReactionRepository {
	constructor(
		@InjectRepository(CommentReaction)
		private readonly reactionRepository: Repository<CommentReaction>
	) {}

	async create(reaction: Partial<ICommentReaction>): Promise<ICommentReaction> {
		const newReaction = this.reactionRepository.create(reaction);
		return await this.reactionRepository.save(newReaction);
	}

	async delete(id: string): Promise<void> {
		await this.reactionRepository.delete(id);
	}

	async findById(id: string): Promise<ICommentReaction> {
		return await this.reactionRepository.findOne({
			where: { id },
		});
	}

	async findByCommentAndUser(commentId: string, userId: string): Promise<ICommentReaction> {
		return await this.reactionRepository.findOne({
			where: { commentId, userId: Number(userId) },
		});
	}

	async findAllByCommentId(commentId: string): Promise<ICommentReaction[]> {
		return await this.reactionRepository.find({
			where: { commentId },
		});
	}
}
