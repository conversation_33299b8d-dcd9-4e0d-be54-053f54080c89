import { ApiProperty } from "@nestjs/swagger";
import { UserResponseDto } from "../../../user/models/dtos/user-response.dto";
import { IUserProfileStats } from "../interfaces/user-profile-stats.interface";
import { IReadingStats } from "../../../reading/use-cases/get-reading-stats/get-reading-stats.use-case";

/**
 * DTO para as estatísticas de leitura (usado na documentação)
 */
export class ReadingStatsDto implements IReadingStats {
	@ApiProperty({
		description: "Quantidade de obras sendo lidas atualmente",
		example: 5,
		type: "integer",
		minimum: 0,
	})
	reading: number;

	@ApiProperty({
		description: "Quantidade de obras completadas",
		example: 12,
		type: "integer",
		minimum: 0,
	})
	completed: number;

	@ApiProperty({
		description: "Quantidade de obras abandonadas",
		example: 2,
		type: "integer",
		minimum: 0,
	})
	dropped: number;

	@ApiProperty({
		description: "Quantidade de obras planejadas para ler",
		example: 8,
		type: "integer",
		minimum: 0,
	})
	planToRead: number;

	@ApiProperty({
		description: "Quantidade de obras pausadas",
		example: 1,
		type: "integer",
		minimum: 0,
	})
	onHold: number;

	@ApiProperty({
		description: "Total de obras na biblioteca do usuário",
		example: 28,
		type: "integer",
		minimum: 0,
	})
	totalWorks: number;
}

/**
 * DTO para as estatísticas completas do usuário (usado na documentação)
 */
export class UserProfileStatsDto implements IUserProfileStats {
	@ApiProperty({
		description: "Estatísticas de leitura por status",
		type: ReadingStatsDto,
	})
	readingStats: ReadingStatsDto;

	@ApiProperty({
		description: "Total de capítulos lidos pelo usuário",
		example: 245,
		type: "integer",
		minimum: 0,
	})
	totalChaptersRead: number;

	@ApiProperty({
		description: "Total de listas criadas pelo usuário",
		example: 3,
		type: "integer",
		minimum: 0,
	})
	totalLists: number;

	@ApiProperty({
		description: "Total de listas públicas criadas pelo usuário",
		example: 2,
		type: "integer",
		minimum: 0,
	})
	totalPublicLists: number;

	@ApiProperty({
		description: "Total de rankings criados pelo usuário",
		example: 2,
		type: "integer",
		minimum: 0,
	})
	totalRankings: number;

	@ApiProperty({
		description: "Total de rankings públicos criados pelo usuário",
		example: 1,
		type: "integer",
		minimum: 0,
	})
	totalPublicRankings: number;

	@ApiProperty({
		description: "Total de reviews escritas pelo usuário",
		example: 8,
		type: "integer",
		minimum: 0,
	})
	totalReviews: number;

	@ApiProperty({
		description: "Total de reviews públicas escritas pelo usuário",
		example: 6,
		type: "integer",
		minimum: 0,
	})
	totalPublicReviews: number;

	@ApiProperty({
		description: "Média das avaliações dadas pelo usuário",
		example: 4.2,
		type: "number",
		minimum: 0,
		maximum: 5,
		required: false,
		nullable: true,
	})
	averageRating?: number;

	@ApiProperty({
		description: "Total de likes recebidos em reviews",
		example: 15,
		type: "integer",
		minimum: 0,
	})
	totalReviewLikes: number;

	@ApiProperty({
		description: "Tempo médio de leitura em dias para obras completadas",
		example: 14.5,
		type: "number",
		minimum: 0,
		required: false,
		nullable: true,
	})
	averageReadingTime?: number;

	@ApiProperty({
		description: "Data da última atividade de leitura",
		example: "2024-06-15T10:30:00.000Z",
		type: "string",
		format: "date-time",
		required: false,
		nullable: true,
	})
	lastReadingActivity?: Date;

	@ApiProperty({
		description: "Gênero/tag mais lido pelo usuário",
		example: "Ação",
		type: "string",
		required: false,
		nullable: true,
	})
	favoriteGenre?: string;
}

/**
 * DTO de resposta para o perfil expandido do usuário
 * Inclui dados básicos do usuário + estatísticas detalhadas
 */
export class UserProfileResponseDto extends UserResponseDto {
	@ApiProperty({
		description: "Estatísticas completas do usuário",
		type: UserProfileStatsDto,
		example: {
			readingStats: {
				reading: 5,
				completed: 12,
				dropped: 2,
				planToRead: 8,
				onHold: 1,
				totalWorks: 28,
			},
			totalChaptersRead: 245,
			totalLists: 3,
			totalPublicLists: 2,
			totalRankings: 2,
			totalPublicRankings: 1,
			totalReviews: 8,
			totalPublicReviews: 6,
			averageRating: 4.2,
			totalReviewLikes: 15,
			averageReadingTime: 14.5,
			lastReadingActivity: "2024-06-15T10:30:00.000Z",
			favoriteGenre: "Ação",
		},
	})
	stats: IUserProfileStats;
}
