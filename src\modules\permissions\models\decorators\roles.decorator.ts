import { SetMetadata } from "@nestjs/common";
import { UserRole } from "../../../user/models/enums";

/**
 * Chave para armazenar metadados dos roles requeridos
 */
export const ROLES_KEY = "roles";

/**
 * Decorator para definir quais roles são necessários para acessar um endpoint
 *
 * @param roles - Array de roles que podem acessar o endpoint
 *
 * @example
 * ```typescript
 * @Roles(UserRole.ADMIN, UserRole.MODERATOR)
 * @Get('admin-only')
 * adminOnlyEndpoint() {
 *   return 'Apenas admins e moderadores podem acessar';
 * }
 * ```
 *
 * @example
 * ```typescript
 * @Roles(UserRole.SUPER_ADMIN)
 * @Delete('dangerous-action')
 * dangerousAction() {
 *   return 'Apenas super admins podem executar esta ação';
 * }
 * ```
 */
export const Roles = (...roles: UserRole[]) => SetMetadata(ROLES_KEY, roles);
