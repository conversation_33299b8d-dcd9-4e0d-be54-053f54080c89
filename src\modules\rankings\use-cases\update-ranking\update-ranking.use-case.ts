import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { UpdateRankingDto } from "../../models/dtos";
import { IRanking, IRankingRepository } from "../../models/interfaces";

@Injectable()
export class UpdateRankingUseCase {
	constructor(
		@Inject("IRankingRepository")
		private rankingRepository: IRankingRepository
	) {}

	async execute(id: string, userId: string, updateRankingDto: UpdateRankingDto): Promise<IRanking> {
		const existingRanking = await this.rankingRepository.findById(id);
		if (!existingRanking) {
			throw new ResourceNotFoundException("Ranking", id);
		}
		// Verificar se o ranking pertence ao usuário
		if (existingRanking.userId !== Number(userId)) {
			throw new ResourceNotFoundException("Ranking do usuário", `${id}:${userId}`);
		}

		return await this.rankingRepository.update(id, updateRankingDto);
	}
}
