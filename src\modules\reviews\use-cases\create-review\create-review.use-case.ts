import { Inject, Injectable } from "@nestjs/common";
import { DuplicateResourceException } from "src/shared/exceptions/business.exceptions";
import { CreateReviewDto } from "../../models/dtos";
import { IReview, IReviewRepository } from "../../models/interfaces";

@Injectable()
export class CreateReviewUseCase {
	constructor(
		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository
	) {}

	async execute(userId: string, createReviewDto: CreateReviewDto): Promise<IReview> {
		// Verificar se o usuário já tem uma review para esta obra
		const existingReview = await this.reviewRepository.findByUserAndWork(parseInt(userId, 10), createReviewDto.workId);
		if (existingReview) {
			throw new DuplicateResourceException("Avaliação", "workId", createReviewDto.workId);
		}

		// Criar a review
		const review = {
			userId: parseInt(userId, 10),
			workId: createReviewDto.workId,
			rating: createReviewDto.rating,
			title: createReviewDto.title,
			content: createReviewDto.content,
			isPublic: createReviewDto.isPublic ?? true,
			hasContainsSpoilers: createReviewDto.hasContainsSpoilers ?? false,
			likes: 0,
			dislikes: 0,
		};

		return await this.reviewRepository.create(review);
	}
}
