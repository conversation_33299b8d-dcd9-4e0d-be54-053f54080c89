import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from "@nestjs/typeorm";

@Injectable()
export class DatabaseConfigService implements TypeOrmOptionsFactory {
	constructor(private configService: ConfigService) {}

	createTypeOrmOptions(): TypeOrmModuleOptions {
		const isDevelopment = this.configService.get<string>("NODE_ENV") === "development";

		return {
			type: "postgres",
			host: this.configService.get<string>("DATABASE_HOST"),
			port: this.configService.get<number>("DATABASE_PORT"),
			username: this.configService.get<string>("DATABASE_USERNAME"),
			password: this.configService.get<string>("DATABASE_PASSWORD"),
			database: this.configService.get<string>("DATABASE_NAME"),
			entities: [__dirname + "/../../**/*.entity.{js,ts}"],
			synchronize: false, // Usa migrações para gerenciar o schema (NUNCA true em produção)
			// dropSchema removido - extremamente perigoso para dados
			migrations: [__dirname + "/../../migrations/*.{js,ts}"],
			migrationsRun: false, // Desabilitado temporariamente para resolver conflitos de migração
			logging: isDevelopment, // Logs apenas em desenvolvimento
		};
	}
}
