import { ApiProperty } from "@nestjs/swagger";
import { Permission } from "../enums";

/**
 * DTO de resposta para uma permissão
 */
export class PermissionResponseDto {
	@ApiProperty({
		description: "ID único da permissão",
		example: "550e8400-e29b-41d4-a716-************",
		format: "uuid",
	})
	id: string;

	@ApiProperty({
		description: "Nome da permissão",
		example: Permission.USERS_READ,
		enum: Permission,
		enumName: "Permission",
	})
	name: Permission;

	@ApiProperty({
		description: "Descrição da permissão",
		example: "Permite visualizar informações de usuários",
		nullable: true,
	})
	description?: string;

	@ApiProperty({
		description: "Recurso relacionado à permissão",
		example: "users",
		nullable: true,
	})
	resource?: string;

	@ApiProperty({
		description: "Ação relacionada à permissão",
		example: "read",
		nullable: true,
	})
	action?: string;

	@ApiProperty({
		description: "Se a permissão está ativa",
		example: true,
	})
	isActive: boolean;

	@ApiProperty({
		description: "Data de criação da permissão",
		example: "2024-06-10T08:00:00.000Z",
		format: "date-time",
	})
	createdAt: Date;

	@ApiProperty({
		description: "Data da última atualização",
		example: "2024-06-12T10:30:00.000Z",
		format: "date-time",
	})
	updatedAt: Date;
}
