import { Inject, Injectable } from "@nestjs/common";
import { BaseUseCase } from "../../../../shared/use-cases/base.use-case";
import { IImageRepository } from "../../models/interfaces/image-repository.interface";
import { ImageResponseDto } from "../../models/dtos";
import { Image } from "../../models/entities/image.entity";
import { ImageStatus } from "../../models/enums";
import { ResourceNotFoundException, BusinessException } from "../../../../shared/exceptions/business.exceptions";
import * as fs from "fs-extra";
import * as path from "path";

/**
 * Interface para resposta de arquivo de imagem
 */
export interface IImageFileResponse {
	filePath: string;
	mimeType: string;
	filename: string;
	size: number;
}

/**
 * Use Case para recuperar imagem
 * Seguindo princípios SOLID - Single Responsibility
 */
@Injectable()
export class GetImageUseCase extends BaseUseCase<number, ImageResponseDto> {
	private readonly baseUploadPath: string;

	constructor(
		@Inject("IImageRepository")
		private readonly imageRepository: IImageRepository
	) {
		super("GetImageUseCase");
		this.baseUploadPath = path.join(process.cwd(), "uploads", "images");
	}

	async execute(imageId: number): Promise<ImageResponseDto> {
		return this.executeWithLogging("GetImage", imageId, async () => {
			// Buscar imagem no banco
			const image = await this.imageRepository.findById(imageId);

			if (!image) {
				throw new ResourceNotFoundException("Imagem", imageId);
			}

			// Verificar se imagem está disponível
			if (!image.isAvailable()) {
				throw new BusinessException("Imagem não está disponível para visualização");
			}

			// Verificar se arquivo existe no sistema
			const fullPath = this.getFullPath(image.path);
			const fileExists = await this.fileExists(fullPath);

			if (!fileExists) {
				// Marcar imagem como com falha se arquivo não existe
				await this.imageRepository.updateStatus(imageId, ImageStatus.FAILED);
				throw new BusinessException("Arquivo de imagem não encontrado no sistema");
			}

			return this.mapToResponseDto(image);
		});
	}

	/**
	 * Método para obter arquivo físico da imagem
	 */
	async getImageFile(imageId: number): Promise<IImageFileResponse> {
		// Primeiro obter dados da imagem
		const imageData = await this.execute(imageId);

		// Obter caminho completo do arquivo
		const fullPath = this.getFullPath(imageData.path);

		return {
			filePath: fullPath,
			mimeType: imageData.mimeType,
			filename: imageData.originalName,
			size: imageData.size,
		};
	}

	private mapToResponseDto(image: Image): ImageResponseDto {
		return {
			id: image.id,
			filename: image.filename,
			originalName: image.originalName,
			path: image.path,
			url: image.url || "",
			mimeType: image.mimeType,
			size: image.size,
			formattedSize: image.getFormattedSize(),
			width: image.width,
			height: image.height,
			dimensions: image.getDimensions(),
			status: image.status,
			hash: image.hash,
			metadata: image.metadata,
			uploadedBy: image.uploadedBy,
			createdAt: image.createdAt,
			updatedAt: image.updatedAt,
			deletedAt: image.deletedAt,
		};
	}

	// ========== MÉTODOS UTILITÁRIOS ==========

	/**
	 * Obtém caminho completo do arquivo
	 */
	private getFullPath(relativePath: string): string {
		return path.join(this.baseUploadPath, relativePath);
	}

	/**
	 * Verifica se arquivo existe
	 */
	private async fileExists(filePath: string): Promise<boolean> {
		try {
			await fs.access(filePath);
			return true;
		} catch {
			return false;
		}
	}
}

/**
 * Use Case para listar imagens com filtros
 */
@Injectable()
export class ListImagesUseCase extends BaseUseCase<any, any> {
	constructor(
		@Inject("IImageRepository")
		private readonly imageRepository: IImageRepository
	) {
		super("ListImagesUseCase");
	}

	async execute(filters: { page?: number; limit?: number; status?: ImageStatus; uploaderId?: number }): Promise<{
		images: ImageResponseDto[];
		total: number;
		totalPages: number;
		currentPage: number;
	}> {
		return this.executeWithLogging("ListImages", filters, async () => {
			const { page = 1, limit = 10, status, uploaderId } = filters;

			let images: Image[];
			let total: number;

			if (uploaderId) {
				// Buscar por usuário específico
				const userImages = await this.imageRepository.findByUploader(uploaderId);
				const filteredImages = status ? userImages.filter(img => img.status === status) : userImages;

				// Aplicar paginação manual
				const startIndex = (page - 1) * limit;
				const endIndex = startIndex + limit;
				images = filteredImages.slice(startIndex, endIndex);
				total = filteredImages.length;
			} else {
				// Usar paginação do repositório
				const result = await this.imageRepository.findWithPagination(page, limit, status);
				images = result.images;
				total = result.total;
			}

			const totalPages = Math.ceil(total / limit);

			return {
				images: images.map(image => this.mapToResponseDto(image)),
				total,
				totalPages,
				currentPage: page,
			};
		});
	}

	private mapToResponseDto(image: Image): ImageResponseDto {
		return {
			id: image.id,
			filename: image.filename,
			originalName: image.originalName,
			path: image.path,
			url: image.url || "",
			mimeType: image.mimeType,
			size: image.size,
			formattedSize: image.getFormattedSize(),
			width: image.width,
			height: image.height,
			dimensions: image.getDimensions(),
			status: image.status,
			hash: image.hash,
			metadata: image.metadata,
			uploadedBy: image.uploadedBy,
			createdAt: image.createdAt,
			updatedAt: image.updatedAt,
			deletedAt: image.deletedAt,
		};
	}
}
