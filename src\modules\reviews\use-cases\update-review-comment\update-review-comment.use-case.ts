import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { UpdateReviewCommentDto } from "../../models/dtos";
import { IReviewComment, IReviewCommentRepository } from "../../models/interfaces";

@Injectable()
export class UpdateReviewCommentUseCase {
	constructor(
		@Inject("IReviewCommentRepository")
		private readonly commentRepository: IReviewCommentRepository
	) {}

	async execute(id: string, userId: string, updateCommentDto: UpdateReviewCommentDto): Promise<IReviewComment> {
		// Verificar se o comentário existe
		const comment = await this.commentRepository.findById(id);

		if (!comment) {
			throw new ResourceNotFoundException("Comentário", id);
		}

		// Verificar se o comentário pertence ao usuário atual
		if (comment.userId !== parseInt(userId)) {
			throw new ResourceNotFoundException("Comentário do usuário", `${id}:${userId}`);
		}

		// Atualizar o comentário
		return await this.commentRepository.update(id, updateCommentDto);
	}
}
