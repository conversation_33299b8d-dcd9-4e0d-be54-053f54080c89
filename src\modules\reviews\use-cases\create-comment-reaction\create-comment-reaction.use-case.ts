import { Inject, Injectable } from "@nestjs/common";
import { DuplicateResourceException, ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { CreateCommentReactionDto } from "../../models/dtos";
import { ICommentReaction, ICommentReactionRepository, IReviewCommentRepository } from "../../models/interfaces";

@Injectable()
export class CreateCommentReactionUseCase {
	constructor(
		@Inject("ICommentReactionRepository")
		private readonly reactionRepository: ICommentReactionRepository,

		@Inject("IReviewCommentRepository")
		private readonly commentRepository: IReviewCommentRepository
	) {}

	async execute(userId: string, createReactionDto: CreateCommentReactionDto): Promise<ICommentReaction> {
		// Verificar se o comentário existe
		const comment = await this.commentRepository.findById(createReactionDto.commentId);
		if (!comment) {
			throw new ResourceNotFoundException("Comentário", createReactionDto.commentId);
		}

		// Verificar se o usuário já reagiu a este comentário
		const existingReaction = await this.reactionRepository.findByCommentAndUser(createReactionDto.commentId, userId);

		if (existingReaction) {
			// Se for o mesmo tipo de reação, retorna erro
			if (existingReaction.isLike === createReactionDto.isLike) {
				throw new DuplicateResourceException("Reação", "tipo", createReactionDto.isLike ? "like" : "dislike");
			}

			// Se for uma reação diferente, remove a anterior e ajusta os contadores
			await this.reactionRepository.delete(existingReaction.id);

			if (existingReaction.isLike) {
				await this.commentRepository.decrementLikes(createReactionDto.commentId);
			} else {
				await this.commentRepository.decrementDislikes(createReactionDto.commentId);
			}
		}

		// Criar a reação
		const reaction = {
			userId: Number(userId),
			commentId: createReactionDto.commentId,
			isLike: createReactionDto.isLike,
		};

		// Atualizar os contadores no comentário
		if (createReactionDto.isLike) {
			await this.commentRepository.incrementLikes(createReactionDto.commentId);
		} else {
			await this.commentRepository.incrementDislikes(createReactionDto.commentId);
		}

		return await this.reactionRepository.create(reaction);
	}
}
