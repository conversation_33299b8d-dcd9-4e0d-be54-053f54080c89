import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IRankingItemRepository, IRankingRepository } from "../../models/interfaces";

@Injectable()
export class RemoveItemFromRankingUseCase {
	constructor(
		@Inject("IRankingRepository")
		private rankingRepository: IRankingRepository,

		@Inject("IRankingItemRepository")
		private rankingItemRepository: IRankingItemRepository
	) {}

	async execute(rankingId: string, itemId: string, userId: string): Promise<void> {
		// Verificar se o ranking existe e pertence ao usuário
		const ranking = await this.rankingRepository.findById(rankingId);
		if (!ranking) {
			throw new ResourceNotFoundException("Ranking", rankingId);
		}
		if (ranking.userId !== Number(userId)) {
			throw new ResourceNotFoundException("Ranking do usuário", `${rankingId}:${userId}`);
		}

		// Verificar se o item existe e pertence ao ranking
		const item = await this.rankingItemRepository.findById(itemId);
		if (!item || item.rankingId !== rankingId) {
			throw new ResourceNotFoundException("Item do ranking", `${itemId}:${rankingId}`);
		}

		// Remover o item
		await this.rankingItemRepository.delete(itemId);

		// Decrementar o contador de itens no ranking
		await this.rankingRepository.decrementItemsCount(rankingId);
	}
}
