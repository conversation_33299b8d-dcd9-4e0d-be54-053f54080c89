import { SetMetadata } from "@nestjs/common";
import { Permission } from "../enums";

/**
 * Chave para armazenar metadados das permissões requeridas
 */
export const PERMISSIONS_KEY = "permissions";

/**
 * Decorator para definir quais permissões são necessárias para acessar um endpoint
 * 
 * @param permissions - Array de permissões que são necessárias para acessar o endpoint
 * 
 * @example
 * ```typescript
 * @RequirePermissions(Permission.USERS_CREATE, Permission.USERS_UPDATE)
 * @Post('create-user')
 * createUser() {
 *   return 'Usuário criado com sucesso';
 * }
 * ```
 * 
 * @example
 * ```typescript
 * @RequirePermissions(Permission.WORKS_DELETE)
 * @Delete('works/:id')
 * deleteWork() {
 *   return 'Obra deletada com sucesso';
 * }
 * ```
 * 
 * @example
 * ```typescript
 * @RequirePermissions(Permission.ADMIN_SYSTEM_CONFIG)
 * @Patch('system/config')
 * updateSystemConfig() {
 *   return 'Configuração do sistema atualizada';
 * }
 * ```
 */
export const RequirePermissions = (...permissions: Permission[]) => SetMetadata(PERMISSIONS_KEY, permissions);
