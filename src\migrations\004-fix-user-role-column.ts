import { MigrationInterface, QueryRunner } from "typeorm";

export class FixUserRoleColumn1734346160000 implements MigrationInterface {
	name = "FixUserRoleColumn1734346160000";

	public async up(queryRunner: QueryRunner): Promise<void> {
		// Verificar se o enum existe, se não, criar
		await queryRunner.query(`
			DO $$ BEGIN
				CREATE TYPE "user_role_enum" AS ENUM('user', 'moderator', 'admin', 'super_admin');
			EXCEPTION
				WHEN duplicate_object THEN null;
			END $$;
		`);

		// Verificar se a coluna role já existe
		const columnExists = await queryRunner.query(`
			SELECT column_name 
			FROM information_schema.columns 
			WHERE table_name = 'users' 
			AND column_name = 'role' 
			AND table_schema = 'public';
		`);

		// Se a coluna não existir, adicioná-la
		if (columnExists.length === 0) {
			await queryRunner.query(`
				ALTER TABLE "users" 
				ADD COLUMN "role" "user_role_enum" NOT NULL DEFAULT 'user'
			`);
		}
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Remover a coluna role
		await queryRunner.query(`ALTER TABLE "users" DROP COLUMN IF EXISTS "role"`);

		// Remover o enum se não estiver sendo usado em outras tabelas
		await queryRunner.query(`DROP TYPE IF EXISTS "user_role_enum"`);
	}
}
