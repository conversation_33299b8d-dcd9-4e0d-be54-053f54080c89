import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IUserWork, IUserWorkRepository } from "../../models/interfaces";

@Injectable()
export class UpdateProgressUseCase {
	constructor(
		@Inject("IUserWorkRepository")
		private readonly userWorkRepository: IUserWorkRepository
	) {}

	async execute(userId: string, workId: string, currentChapter: number): Promise<IUserWork> {
		// Verificar se o usuário tem esta obra na lista
		const userWork = await this.userWorkRepository.findByUserAndWork(Number(userId), workId);
		if (!userWork) {
			throw new ResourceNotFoundException("Obra na lista de leitura", `${userId}:${workId}`);
		}

		return await this.userWorkRepository.updateProgress(Number(userId), workId, currentChapter);
	}
}
