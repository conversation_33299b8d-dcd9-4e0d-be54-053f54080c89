import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, LessThan } from "typeorm";
import { UserRole } from "../models/entities";
import { IUserRoleRepository } from "../models/interfaces";
import { Permission } from "../models/enums";

@Injectable()
export class UserRoleTypeOrmRepository implements IUserRoleRepository {
	constructor(
		@InjectRepository(UserRole)
		private readonly userRoleRepository: Repository<UserRole>
	) {}

	async findById(id: string): Promise<UserRole | null> {
		return this.userRoleRepository.findOne({
			where: { id },
			relations: ["role"],
		});
	}

	async findByUserId(userId: number): Promise<UserRole[]> {
		return this.userRoleRepository.find({
			where: { userId, isActive: true },
			relations: ["role"],
			order: { createdAt: "DESC" },
		});
	}

	async findByRoleId(roleId: string): Promise<UserRole[]> {
		return this.userRoleRepository.find({
			where: { roleId, isActive: true },
			relations: ["role"],
			order: { createdAt: "DESC" },
		});
	}

	async findByUserAndRole(userId: number, roleId: string): Promise<UserRole | null> {
		return this.userRoleRepository.findOne({
			where: { userId, roleId, isActive: true },
			relations: ["role"],
		});
	}

	async findUserRolesWithPermissions(userId: number): Promise<UserRole[]> {
		return this.userRoleRepository.find({
			where: { userId, isActive: true },
			relations: ["role", "role.permissions"],
		});
	}

	async assignRole(userId: number, roleId: string, assignedBy?: number, expiresAt?: Date): Promise<UserRole> {
		// Verifica se já existe uma atribuição ativa
		const existingUserRole = await this.findByUserAndRole(userId, roleId);
		if (existingUserRole) {
			throw new Error("Usuário já possui este role");
		}

		const userRole = this.userRoleRepository.create({
			userId,
			roleId,
			assignedBy,
			expiresAt,
			isActive: true,
		});

		return this.userRoleRepository.save(userRole);
	}

	async removeRole(userId: number, roleId: string): Promise<void> {
		await this.userRoleRepository.update(
			{ userId, roleId },
			{ isActive: false }
		);
	}

	async removeAllUserRoles(userId: number): Promise<void> {
		await this.userRoleRepository.update(
			{ userId },
			{ isActive: false }
		);
	}

	async hasRole(userId: number, roleId: string): Promise<boolean> {
		const count = await this.userRoleRepository.count({
			where: { userId, roleId, isActive: true },
		});
		return count > 0;
	}

	async hasPermission(userId: number, permission: Permission): Promise<boolean> {
		const userRoles = await this.findUserRolesWithPermissions(userId);
		
		for (const userRole of userRoles) {
			if (userRole.role?.permissions?.some(p => p.name === permission)) {
				return true;
			}
		}
		
		return false;
	}

	async getUserPermissions(userId: number): Promise<Permission[]> {
		const userRoles = await this.findUserRolesWithPermissions(userId);
		const permissions = new Set<Permission>();

		for (const userRole of userRoles) {
			if (userRole.role?.permissions) {
				userRole.role.permissions.forEach(p => permissions.add(p.name));
			}
		}

		return Array.from(permissions);
	}

	async update(id: string, data: Partial<UserRole>): Promise<UserRole> {
		await this.userRoleRepository.update(id, data);
		return this.findById(id);
	}

	async findExpiredRoles(): Promise<UserRole[]> {
		return this.userRoleRepository.find({
			where: {
				isActive: true,
				expiresAt: LessThan(new Date()),
			},
			relations: ["role"],
		});
	}

	async removeExpiredRoles(): Promise<void> {
		await this.userRoleRepository.update(
			{
				isActive: true,
				expiresAt: LessThan(new Date()),
			},
			{ isActive: false }
		);
	}
}
