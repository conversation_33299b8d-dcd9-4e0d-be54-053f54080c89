import { MigrationInterface, QueryRunner } from "typeorm";

export class FixUserIdTypes1734700000000 implements MigrationInterface {
	name = "FixUserIdTypes1734700000000";

	public async up(queryRunner: QueryRunner): Promise<void> {
		// <PERSON><PERSON>, vamos verificar se as tabelas existem e têm dados
		// Se houver dados, precisamos fazer backup e conversão

		// 1. Alterar user_works.userId de uuid para integer
		await queryRunner.query(`
			-- Verificar se a coluna existe e é uuid
			DO $$ 
			BEGIN
				IF EXISTS (
					SELECT 1 FROM information_schema.columns 
					WHERE table_name = 'user_works' 
					AND column_name = 'userId' 
					AND data_type = 'uuid'
				) THEN
					-- Remover constraint de foreign key se existir
					ALTER TABLE "user_works" DROP CONSTRAINT IF EXISTS "FK_user_works_user";
					
					-- Alterar o tipo da coluna
					ALTER TABLE "user_works" ALTER COLUMN "userId" TYPE integer USING CAST(SUBSTRING("userId"::text FROM 1 FOR 10) AS integer);
				END IF;
			END $$;
		`);

		// 2. Alterar lists.userId de uuid para integer
		await queryRunner.query(`
			DO $$ 
			BEGIN
				IF EXISTS (
					SELECT 1 FROM information_schema.columns 
					WHERE table_name = 'lists' 
					AND column_name = 'userId' 
					AND data_type = 'uuid'
				) THEN
					-- Remover constraint de foreign key se existir
					ALTER TABLE "lists" DROP CONSTRAINT IF EXISTS "FK_lists_user";
					
					-- Alterar o tipo da coluna
					ALTER TABLE "lists" ALTER COLUMN "userId" TYPE integer USING CAST(SUBSTRING("userId"::text FROM 1 FOR 10) AS integer);
				END IF;
			END $$;
		`);

		// 3. Alterar rankings.userId de uuid para integer
		await queryRunner.query(`
			DO $$ 
			BEGIN
				IF EXISTS (
					SELECT 1 FROM information_schema.columns 
					WHERE table_name = 'rankings' 
					AND column_name = 'userId' 
					AND data_type = 'uuid'
				) THEN
					-- Remover constraint de foreign key se existir
					ALTER TABLE "rankings" DROP CONSTRAINT IF EXISTS "FK_rankings_user";
					
					-- Alterar o tipo da coluna
					ALTER TABLE "rankings" ALTER COLUMN "userId" TYPE integer USING CAST(SUBSTRING("userId"::text FROM 1 FOR 10) AS integer);
				END IF;
			END $$;
		`);

		// 4. Alterar reviews.userId de uuid para integer
		await queryRunner.query(`
			DO $$ 
			BEGIN
				IF EXISTS (
					SELECT 1 FROM information_schema.columns 
					WHERE table_name = 'reviews' 
					AND column_name = 'userId' 
					AND data_type = 'uuid'
				) THEN
					-- Remover constraint de foreign key se existir
					ALTER TABLE "reviews" DROP CONSTRAINT IF EXISTS "FK_reviews_user";
					
					-- Alterar o tipo da coluna
					ALTER TABLE "reviews" ALTER COLUMN "userId" TYPE integer USING CAST(SUBSTRING("userId"::text FROM 1 FOR 10) AS integer);
				END IF;
			END $$;
		`);

		// 5. Alterar review_reactions.userId de uuid para integer
		await queryRunner.query(`
			DO $$ 
			BEGIN
				IF EXISTS (
					SELECT 1 FROM information_schema.columns 
					WHERE table_name = 'review_reactions' 
					AND column_name = 'userId' 
					AND data_type = 'uuid'
				) THEN
					-- Remover constraint de foreign key se existir
					ALTER TABLE "review_reactions" DROP CONSTRAINT IF EXISTS "FK_review_reactions_user";
					
					-- Alterar o tipo da coluna
					ALTER TABLE "review_reactions" ALTER COLUMN "userId" TYPE integer USING CAST(SUBSTRING("userId"::text FROM 1 FOR 10) AS integer);
				END IF;
			END $$;
		`);

		// 6. Alterar review_comments.userId de uuid para integer
		await queryRunner.query(`
			DO $$ 
			BEGIN
				IF EXISTS (
					SELECT 1 FROM information_schema.columns 
					WHERE table_name = 'review_comments' 
					AND column_name = 'userId' 
					AND data_type = 'uuid'
				) THEN
					-- Remover constraint de foreign key se existir
					ALTER TABLE "review_comments" DROP CONSTRAINT IF EXISTS "FK_review_comments_user";
					
					-- Alterar o tipo da coluna
					ALTER TABLE "review_comments" ALTER COLUMN "userId" TYPE integer USING CAST(SUBSTRING("userId"::text FROM 1 FOR 10) AS integer);
				END IF;
			END $$;
		`);

		// 7. Alterar comment_reactions.userId de uuid para integer
		await queryRunner.query(`
			DO $$ 
			BEGIN
				IF EXISTS (
					SELECT 1 FROM information_schema.columns 
					WHERE table_name = 'comment_reactions' 
					AND column_name = 'userId' 
					AND data_type = 'uuid'
				) THEN
					-- Remover constraint de foreign key se existir
					ALTER TABLE "comment_reactions" DROP CONSTRAINT IF EXISTS "FK_comment_reactions_user";
					
					-- Alterar o tipo da coluna
					ALTER TABLE "comment_reactions" ALTER COLUMN "userId" TYPE integer USING CAST(SUBSTRING("userId"::text FROM 1 FOR 10) AS integer);
				END IF;
			END $$;
		`);

		// Recriar foreign keys se a tabela users existir
		await queryRunner.query(`
			DO $$ 
			BEGIN
				IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN
					-- Adicionar foreign keys de volta
					ALTER TABLE "user_works" ADD CONSTRAINT "FK_user_works_user" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE;
					ALTER TABLE "lists" ADD CONSTRAINT "FK_lists_user" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE;
					ALTER TABLE "rankings" ADD CONSTRAINT "FK_rankings_user" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE;
					ALTER TABLE "reviews" ADD CONSTRAINT "FK_reviews_user" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE;
					ALTER TABLE "review_reactions" ADD CONSTRAINT "FK_review_reactions_user" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE;
					ALTER TABLE "review_comments" ADD CONSTRAINT "FK_review_comments_user" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE;
					ALTER TABLE "comment_reactions" ADD CONSTRAINT "FK_comment_reactions_user" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE;
				END IF;
			EXCEPTION
				WHEN duplicate_object THEN null;
			END $$;
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Reverter as mudanças - converter de volta para uuid
		// ATENÇÃO: Esta operação pode causar perda de dados se os IDs não forem compatíveis

		// Remover foreign keys
		await queryRunner.query(`ALTER TABLE "user_works" DROP CONSTRAINT IF EXISTS "FK_user_works_user"`);
		await queryRunner.query(`ALTER TABLE "lists" DROP CONSTRAINT IF EXISTS "FK_lists_user"`);
		await queryRunner.query(`ALTER TABLE "rankings" DROP CONSTRAINT IF EXISTS "FK_rankings_user"`);
		await queryRunner.query(`ALTER TABLE "reviews" DROP CONSTRAINT IF EXISTS "FK_reviews_user"`);
		await queryRunner.query(`ALTER TABLE "review_reactions" DROP CONSTRAINT IF EXISTS "FK_review_reactions_user"`);
		await queryRunner.query(`ALTER TABLE "review_comments" DROP CONSTRAINT IF EXISTS "FK_review_comments_user"`);
		await queryRunner.query(`ALTER TABLE "comment_reactions" DROP CONSTRAINT IF EXISTS "FK_comment_reactions_user"`);

		// Converter de volta para uuid (pode falhar se os dados não forem compatíveis)
		await queryRunner.query(`ALTER TABLE "user_works" ALTER COLUMN "userId" TYPE uuid USING uuid_generate_v4()`);
		await queryRunner.query(`ALTER TABLE "lists" ALTER COLUMN "userId" TYPE uuid USING uuid_generate_v4()`);
		await queryRunner.query(`ALTER TABLE "rankings" ALTER COLUMN "userId" TYPE uuid USING uuid_generate_v4()`);
		await queryRunner.query(`ALTER TABLE "reviews" ALTER COLUMN "userId" TYPE uuid USING uuid_generate_v4()`);
		await queryRunner.query(`ALTER TABLE "review_reactions" ALTER COLUMN "userId" TYPE uuid USING uuid_generate_v4()`);
		await queryRunner.query(`ALTER TABLE "review_comments" ALTER COLUMN "userId" TYPE uuid USING uuid_generate_v4()`);
		await queryRunner.query(`ALTER TABLE "comment_reactions" ALTER COLUMN "userId" TYPE uuid USING uuid_generate_v4()`);
	}
}
