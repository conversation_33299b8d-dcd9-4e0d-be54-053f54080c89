import { WorkType, WorkStatus } from "../enums";
import { Image } from "../../../images/models/entities/image.entity";

export interface IWork {
	id: string;
	title: string;
	originalTitle?: string;
	description?: string;
	coverImage?: string;
	coverImageId?: number;
	coverImageEntity?: Image;
	galleryImages?: Image[];
	type: WorkType;
	status: WorkStatus;
	author?: string;
	artist?: string;
	totalChapters?: number;
	releaseDate?: Date;
	averageRating?: number;
	totalReviews: number;
	createdAt: Date;
	updatedAt: Date;
}

export interface ICreateWorkRequest {
	title: string;
	originalTitle?: string;
	description?: string;
	coverImage?: string;
	coverImageId?: number;
	galleryImageIds?: number[];
	type: WorkType;
	status: WorkStatus;
	author?: string;
	artist?: string;
	totalChapters?: number;
	releaseDate?: Date;
}

export interface IUpdateWorkRequest {
	title?: string;
	originalTitle?: string;
	description?: string;
	coverImage?: string;
	coverImageId?: number;
	galleryImageIds?: number[];
	status?: WorkStatus;
	author?: string;
	artist?: string;
	totalChapters?: number;
	releaseDate?: Date;
}

export interface IWorkFilters {
	type?: WorkType;
	status?: WorkStatus;
	author?: string;
	artist?: string;
	search?: string;
	tags?: string[];
	minRating?: number;
	maxRating?: number;
	page?: number;
	limit?: number;
	sortBy?: "title" | "releaseDate" | "averageRating" | "createdAt";
	sortOrder?: "ASC" | "DESC";
}

// Interfaces para associação de imagens
export interface IAssociateImageRequest {
	workId: string;
	imageId: number;
	userId: number; // Para validação de propriedade
}

export interface IAssociateCoverImageRequest {
	workId: string;
	imageId: number;
	userId: number;
}

export interface IAssociateGalleryImagesRequest {
	workId: string;
	imageIds: number[];
	userId: number;
}

export interface IWorkRepository {
	create(data: ICreateWorkRequest): Promise<IWork>;
	findById(id: string): Promise<IWork | null>;
	findByIdWithImages(id: string): Promise<IWork | null>;
	findAll(filters: IWorkFilters): Promise<{ works: IWork[]; total: number }>;
	update(id: string, data: IUpdateWorkRequest): Promise<IWork>;
	delete(id: string): Promise<void>;
	findByTitle(title: string): Promise<IWork | null>;
	updateAverageRating(id: string): Promise<void>;
	associateCoverImage(workId: string, imageId: number): Promise<void>;
	associateGalleryImages(workId: string, imageIds: number[]): Promise<void>;
	removeCoverImage(workId: string): Promise<void>;
	removeGalleryImage(workId: string, imageId: number): Promise<void>;
	removeAllGalleryImages(workId: string): Promise<void>;
}
