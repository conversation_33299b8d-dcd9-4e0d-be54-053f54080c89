import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsEnum, IsNumber, Min, Max, IsString, IsDateString } from "class-validator";
import { Transform } from "class-transformer";
import { ImageStatus, ImageType } from "../enums";

/**
 * DTO para filtros de busca de imagens
 * Seguindo princípios SOLID - Single Responsibility
 */
export class ImageFilterDto {
	@ApiPropertyOptional({
		description: "Página para paginação",
		example: 1,
		minimum: 1,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsNumber()
	@Min(1)
	page?: number = 1;

	@ApiPropertyOptional({
		description: "Limite de itens por página",
		example: 10,
		minimum: 1,
		maximum: 100,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsNumber()
	@Min(1)
	@Max(100)
	limit?: number = 10;

	@ApiPropertyOptional({
		description: "Filtrar por status da imagem",
		enum: ImageStatus,
		example: ImageStatus.ACTIVE,
	})
	@IsOptional()
	@IsEnum(ImageStatus)
	status?: ImageStatus;

	@ApiPropertyOptional({
		description: "Filtrar por tipo MIME",
		enum: ImageType,
		example: ImageType.JPEG,
	})
	@IsOptional()
	@IsEnum(ImageType)
	mimeType?: ImageType;

	@ApiPropertyOptional({
		description: "Filtrar por usuário que fez upload",
		example: 1,
		minimum: 1,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsNumber()
	@Min(1)
	uploadedBy?: number;

	@ApiPropertyOptional({
		description: "Buscar por nome do arquivo (original ou gerado)",
		example: "avatar",
	})
	@IsOptional()
	@IsString()
	filename?: string;

	@ApiPropertyOptional({
		description: "Data de início para filtro por período (ISO 8601)",
		example: "2024-01-01T00:00:00.000Z",
	})
	@IsOptional()
	@IsDateString()
	startDate?: string;

	@ApiPropertyOptional({
		description: "Data de fim para filtro por período (ISO 8601)",
		example: "2024-12-31T23:59:59.999Z",
	})
	@IsOptional()
	@IsDateString()
	endDate?: string;

	@ApiPropertyOptional({
		description: "Tamanho mínimo do arquivo em bytes",
		example: 1024,
		minimum: 0,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsNumber()
	@Min(0)
	minSize?: number;

	@ApiPropertyOptional({
		description: "Tamanho máximo do arquivo em bytes",
		example: 10485760,
		minimum: 1,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsNumber()
	@Min(1)
	maxSize?: number;

	@ApiPropertyOptional({
		description: "Largura mínima da imagem",
		example: 100,
		minimum: 1,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsNumber()
	@Min(1)
	minWidth?: number;

	@ApiPropertyOptional({
		description: "Largura máxima da imagem",
		example: 2000,
		minimum: 1,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsNumber()
	@Min(1)
	maxWidth?: number;

	@ApiPropertyOptional({
		description: "Altura mínima da imagem",
		example: 100,
		minimum: 1,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsNumber()
	@Min(1)
	minHeight?: number;

	@ApiPropertyOptional({
		description: "Altura máxima da imagem",
		example: 2000,
		minimum: 1,
	})
	@IsOptional()
	@Transform(({ value }) => parseInt(value))
	@IsNumber()
	@Min(1)
	maxHeight?: number;
}

/**
 * DTO para ordenação de imagens
 */
export class ImageSortDto {
	@ApiPropertyOptional({
		description: "Campo para ordenação",
		example: "createdAt",
		enum: ["id", "filename", "originalName", "size", "createdAt", "updatedAt"],
	})
	@IsOptional()
	@IsString()
	sortBy?: string = "createdAt";

	@ApiPropertyOptional({
		description: "Direção da ordenação",
		example: "DESC",
		enum: ["ASC", "DESC"],
	})
	@IsOptional()
	@IsString()
	sortOrder?: "ASC" | "DESC" = "DESC";
}
