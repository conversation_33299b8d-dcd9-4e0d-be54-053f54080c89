import { Injectable, NestMiddleware, BadRequestException } from "@nestjs/common";
import { Request, Response, NextFunction } from "express";
import { SUPPORTED_IMAGE_TYPES, SUPPORTED_IMAGE_EXTENSIONS } from "../models/enums";

/**
 * Interface para configuração do middleware
 */
export interface IImageValidationConfig {
	maxSizeInMB?: number;
	allowedMimeTypes?: string[];
	allowedExtensions?: string[];
	requireImage?: boolean;
}

/**
 * Middleware para validação de upload de imagens
 * Seguindo princípios SOLID - Single Responsibility
 */
@Injectable()
export class ImageValidationMiddleware implements NestMiddleware {
	private readonly config: IImageValidationConfig;

	constructor(config: IImageValidationConfig = {}) {
		this.config = {
			maxSizeInMB: 10,
			allowedMimeTypes: SUPPORTED_IMAGE_TYPES,
			allowedExtensions: SUPPORTED_IMAGE_EXTENSIONS,
			requireImage: true,
			...config,
		};
	}

	use(req: Request, res: Response, next: NextFunction): void {
		try {
			// Verificar se há arquivo no request
			if (this.config.requireImage && !req.file) {
				throw new BadRequestException("Nenhum arquivo de imagem foi enviado");
			}

			// Se não há arquivo e não é obrigatório, prosseguir
			if (!req.file) {
				return next();
			}

			// Validar tipo MIME
			this.validateMimeType(req.file);

			// Validar extensão do arquivo
			this.validateFileExtension(req.file);

			// Validar tamanho do arquivo
			this.validateFileSize(req.file);

			// Sanitizar nome do arquivo
			this.sanitizeFilename(req.file);

			// Validações adicionais de segurança
			this.performSecurityValidations(req.file);

			next();
		} catch (error) {
			next(error);
		}
	}

	private validateMimeType(file: Express.Multer.File): void {
		if (!this.config.allowedMimeTypes?.includes(file.mimetype)) {
			throw new BadRequestException(
				`Tipo de arquivo não suportado: ${file.mimetype}. ` + `Tipos permitidos: ${this.config.allowedMimeTypes?.join(", ")}`
			);
		}
	}

	private validateFileExtension(file: Express.Multer.File): void {
		const extension = this.getFileExtension(file.originalname);

		if (!this.config.allowedExtensions?.includes(extension)) {
			throw new BadRequestException(
				`Extensão de arquivo não suportada: ${extension}. ` + `Extensões permitidas: ${this.config.allowedExtensions?.join(", ")}`
			);
		}
	}

	private validateFileSize(file: Express.Multer.File): void {
		const maxSizeInBytes = (this.config.maxSizeInMB || 10) * 1024 * 1024;

		if (file.size > maxSizeInBytes) {
			const fileSizeInMB = (file.size / (1024 * 1024)).toFixed(2);
			throw new BadRequestException(`Arquivo muito grande: ${fileSizeInMB}MB. ` + `Tamanho máximo permitido: ${this.config.maxSizeInMB}MB`);
		}
	}

	private sanitizeFilename(file: Express.Multer.File): void {
		// Remover caracteres perigosos e normalizar
		const sanitized = file.originalname
			.replace(/[^a-zA-Z0-9\-_\.]/g, "_") // Substituir caracteres especiais
			.replace(/_{2,}/g, "_") // Remover underscores duplos
			.replace(/^_+|_+$/g, "") // Remover underscores no início/fim
			.toLowerCase();

		// Garantir que o nome não seja muito longo
		const maxLength = 100;
		if (sanitized.length > maxLength) {
			const extension = this.getFileExtension(sanitized);
			const nameWithoutExt = sanitized.substring(0, sanitized.lastIndexOf("."));
			const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length);
			file.originalname = truncatedName + extension;
		} else {
			file.originalname = sanitized;
		}
	}

	private performSecurityValidations(file: Express.Multer.File): void {
		// Verificar se o nome do arquivo não contém caminhos
		if (file.originalname.includes("..") || file.originalname.includes("/") || file.originalname.includes("\\")) {
			throw new BadRequestException("Nome de arquivo contém caracteres não permitidos");
		}

		// Verificar se não é um arquivo executável disfarçado
		const dangerousExtensions = [".exe", ".bat", ".cmd", ".com", ".pif", ".scr", ".vbs", ".js"];
		const extension = this.getFileExtension(file.originalname);

		if (dangerousExtensions.includes(extension)) {
			throw new BadRequestException("Tipo de arquivo não permitido por questões de segurança");
		}

		// Verificar tamanho mínimo (evitar arquivos vazios ou muito pequenos)
		if (file.size < 100) {
			// 100 bytes mínimo
			throw new BadRequestException("Arquivo muito pequeno para ser uma imagem válida");
		}
	}

	private getFileExtension(filename: string): string {
		const lastDotIndex = filename.lastIndexOf(".");
		return lastDotIndex !== -1 ? filename.substring(lastDotIndex).toLowerCase() : "";
	}
}

/**
 * Factory para criar middleware com configuração específica
 */
export class ImageValidationMiddlewareFactory {
	static create(config: IImageValidationConfig = {}): ImageValidationMiddleware {
		return new ImageValidationMiddleware(config);
	}

	/**
	 * Configuração para avatares de usuário
	 */
	static forAvatars(): ImageValidationMiddleware {
		return new ImageValidationMiddleware({
			maxSizeInMB: 2,
			allowedMimeTypes: ["image/jpeg", "image/jpg", "image/png", "image/webp"],
			allowedExtensions: [".jpg", ".jpeg", ".png", ".webp"],
			requireImage: true,
		});
	}

	/**
	 * Configuração para imagens de conteúdo
	 */
	static forContent(): ImageValidationMiddleware {
		return new ImageValidationMiddleware({
			maxSizeInMB: 10,
			allowedMimeTypes: SUPPORTED_IMAGE_TYPES,
			allowedExtensions: SUPPORTED_IMAGE_EXTENSIONS,
			requireImage: true,
		});
	}

	/**
	 * Configuração para uploads opcionais
	 */
	static forOptionalUploads(): ImageValidationMiddleware {
		return new ImageValidationMiddleware({
			maxSizeInMB: 5,
			allowedMimeTypes: SUPPORTED_IMAGE_TYPES,
			allowedExtensions: SUPPORTED_IMAGE_EXTENSIONS,
			requireImage: false,
		});
	}
}
