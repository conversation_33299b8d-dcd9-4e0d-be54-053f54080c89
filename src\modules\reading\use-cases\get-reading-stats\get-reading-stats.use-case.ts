import { Injectable, Inject } from "@nestjs/common";
import { IUserWorkRepository } from "../../models/interfaces";

export interface IReadingStats {
	reading: number;
	completed: number;
	dropped: number;
	planToRead: number;
	onHold: number;
	totalWorks: number;
}

@Injectable()
export class GetReadingStatsUseCase {
	constructor(
		@Inject("IUserWorkRepository")
		private readonly userWorkRepository: IUserWorkRepository
	) {}

	async execute(userId: string): Promise<IReadingStats> {
		return await this.userWorkRepository.getReadingStats(Number(userId));
	}
}
