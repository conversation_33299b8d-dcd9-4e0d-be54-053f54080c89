import { IReadingStats } from "../../../reading/use-cases/get-reading-stats/get-reading-stats.use-case";

/**
 * Interface que define as estatísticas completas do perfil do usuário
 */
export interface IUserProfileStats {
	/**
	 * Estatísticas de leitura (obras por status)
	 */
	readingStats: IReadingStats;

	/**
	 * Total de capítulos lidos pelo usuário
	 */
	totalChaptersRead: number;

	/**
	 * Total de listas criadas pelo usuário
	 */
	totalLists: number;

	/**
	 * Total de listas públicas criadas pelo usuário
	 */
	totalPublicLists: number;

	/**
	 * Total de rankings criados pelo usuário
	 */
	totalRankings: number;

	/**
	 * Total de rankings públicos criados pelo usuário
	 */
	totalPublicRankings: number;

	/**
	 * Total de reviews escritas pelo usuário
	 */
	totalReviews: number;

	/**
	 * Total de reviews públicas escritas pelo usuário
	 */
	totalPublicReviews: number;

	/**
	 * Média das avaliações dadas pelo usuário
	 */
	averageRating?: number;

	/**
	 * Total de likes recebidos em reviews
	 */
	totalReviewLikes: number;

	/**
	 * Tempo médio de leitura (em dias) para obras completadas
	 */
	averageReadingTime?: number;

	/**
	 * Data da última atividade de leitura
	 */
	lastReadingActivity?: Date;

	/**
	 * Gênero/tag mais lido pelo usuário
	 */
	favoriteGenre?: string;
}
