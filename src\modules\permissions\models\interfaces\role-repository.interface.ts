import { Role } from "../entities";
import { Permission } from "../enums";

/**
 * Interface para o repositório de roles
 * Define os métodos para gerenciamento de roles no sistema
 */
export interface IRoleRepository {
	/**
	 * Busca um role pelo ID
	 */
	findById(id: string): Promise<Role | null>;

	/**
	 * Busca um role pelo nome
	 */
	findByName(name: string): Promise<Role | null>;

	/**
	 * Lista todos os roles ativos
	 */
	findAll(): Promise<Role[]>;

	/**
	 * Lista roles com suas permissões
	 */
	findAllWithPermissions(): Promise<Role[]>;

	/**
	 * Busca um role com suas permissões
	 */
	findByIdWithPermissions(id: string): Promise<Role | null>;

	/**
	 * Cria um novo role
	 */
	create(roleData: Partial<Role>): Promise<Role>;

	/**
	 * Atualiza um role existente
	 */
	update(id: string, roleData: Partial<Role>): Promise<Role>;

	/**
	 * Remove um role (soft delete)
	 */
	delete(id: string): Promise<void>;

	/**
	 * <PERSON><PERSON><PERSON> permissões a um role
	 */
	addPermissions(roleId: string, permissionIds: string[]): Promise<void>;

	/**
	 * Remove permissões de um role
	 */
	removePermissions(roleId: string, permissionIds: string[]): Promise<void>;

	/**
	 * Verifica se um role tem uma permissão específica
	 */
	hasPermission(roleId: string, permission: Permission): Promise<boolean>;

	/**
	 * Lista todas as permissões de um role
	 */
	getPermissions(roleId: string): Promise<Permission[]>;
}
