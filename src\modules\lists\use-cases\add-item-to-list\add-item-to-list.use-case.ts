import { Inject, Injectable } from "@nestjs/common";
import { DuplicateResourceException, ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { WorkRepository } from "../../../works/repositories";
import { ICreateListItemRequest, IListItem, IListItemRepository, IListRepository } from "../../models/interfaces";

@Injectable()
export class AddItemToListUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository,
		@Inject("IListItemRepository")
		private readonly listItemRepository: IListItemRepository,
		private readonly workRepository: WorkRepository
	) {}

	async execute(userId: number, listId: string, data: Omit<ICreateListItemRequest, "listId">): Promise<IListItem> {
		// Verificar se a lista existe e pertence ao usuário
		const list = await this.listRepository.findById(listId);
		if (!list) {
			throw new ResourceNotFoundException("Lista", listId);
		}
		if (list.userId !== userId) {
			throw new ResourceNotFoundException("Lista do usuário", `${listId}:${userId}`);
		}

		// Verificar se a obra existe
		const work = await this.workRepository.findById(data.workId);
		if (!work) {
			throw new ResourceNotFoundException("Obra", data.workId);
		}
		// Verificar se a obra já não está na lista
		const existingItem = await this.listItemRepository.findByListAndWork(listId, data.workId);
		if (existingItem) {
			throw new DuplicateResourceException("Obra na lista", "workId", data.workId);
		}

		const createData: ICreateListItemRequest = {
			...data,
			listId,
		};

		const listItem = await this.listItemRepository.create(createData);

		// Incrementar contador de itens na lista
		await this.listRepository.incrementItemsCount(listId);

		return listItem;
	}
}
