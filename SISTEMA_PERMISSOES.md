# 🔐 Sistema de Permissões RBAC - Manhwa/Manga Tracker API

## 📋 Visão Geral

Este documento descreve o sistema completo de **Role-Based Access Control (RBAC)** implementado na aplicação de manhwa/manga tracker. O sistema oferece controle granular de acesso através de roles e permissões específicas.

## 🏗️ Arquitetura do Sistema

### **Componentes Principais**

1. **Entidades**
   - `User` - Usuários do sistema (com campo `role` básico)
   - `Role` - Roles/funções do sistema
   - `Permission` - Permissões granulares
   - `UserRole` - Relação many-to-many entre usuários e roles adicionais

2. **Guards**
   - `RolesGuard` - Controla acesso baseado em roles
   - `PermissionsGuard` - Controla acesso baseado em permissões específicas

3. **Decorators**
   - `@Roles()` - Define roles necessários para acessar um endpoint
   - `@RequirePermissions()` - Define permissões específicas necessárias

## 🎭 Roles do Sistema

### **Roles Básicos (Enum UserRole)**

| Role | Descrição | Permissões |
|------|-----------|------------|
| `USER` | Usuário comum | Funcionalidades básicas de leitura e gerenciamento pessoal |
| `MODERATOR` | Moderador | Moderação de conteúdo + permissões de usuário |
| `ADMIN` | Administrador | Gerenciamento completo + permissões de moderador |
| `SUPER_ADMIN` | Super Admin | Todas as permissões do sistema |

### **Roles Avançados (Sistema de Permissões)**

Além dos roles básicos, o sistema suporta roles customizados através da tabela `roles`:

- **Roles do Sistema** (`isSystem: true`) - Não podem ser deletados
- **Roles Customizados** - Criados via API para necessidades específicas

## 🔑 Permissões Granulares

### **Estrutura das Permissões**

Formato: `RESOURCE:ACTION`

### **Categorias de Permissões**

#### **👥 Usuários**
- `users:create` - Criar novos usuários
- `users:read` - Visualizar informações de usuários
- `users:update` - Atualizar dados de usuários
- `users:delete` - Deletar usuários
- `users:list` - Listar usuários
- `users:manage_roles` - Gerenciar roles de usuários

#### **📚 Obras**
- `works:create` - Criar novas obras
- `works:read` - Visualizar obras
- `works:update` - Atualizar obras
- `works:delete` - Deletar obras
- `works:list` - Listar obras
- `works:moderate` - Moderar obras

#### **📖 Leitura**
- `reading:create` - Adicionar à lista de leitura
- `reading:read` - Visualizar progresso
- `reading:update` - Atualizar progresso
- `reading:delete` - Remover da lista
- `reading:list` - Listar histórico

#### **📝 Listas**
- `lists:create` - Criar listas personalizadas
- `lists:read` - Visualizar listas
- `lists:update` - Atualizar listas
- `lists:delete` - Deletar listas
- `lists:list` - Listar todas as listas
- `lists:moderate` - Moderar listas públicas

#### **🏆 Rankings**
- `rankings:create` - Criar rankings
- `rankings:read` - Visualizar rankings
- `rankings:update` - Atualizar rankings
- `rankings:delete` - Deletar rankings
- `rankings:list` - Listar rankings
- `rankings:moderate` - Moderar rankings públicos

#### **⭐ Reviews**
- `reviews:create` - Criar avaliações
- `reviews:read` - Visualizar avaliações
- `reviews:update` - Atualizar avaliações
- `reviews:delete` - Deletar avaliações
- `reviews:list` - Listar avaliações
- `reviews:moderate` - Moderar avaliações

#### **🏷️ Tags**
- `tags:create` - Criar tags
- `tags:read` - Visualizar tags
- `tags:update` - Atualizar tags
- `tags:delete` - Deletar tags
- `tags:list` - Listar tags
- `tags:moderate` - Moderar tags

#### **⚙️ Administração**
- `admin:dashboard` - Acessar dashboard administrativo
- `admin:system_config` - Configurar sistema
- `admin:logs` - Visualizar logs
- `admin:metrics` - Visualizar métricas
- `admin:backup` - Gerenciar backups

#### **🛡️ Moderação**
- `moderate:content` - Moderar conteúdo geral
- `moderate:users` - Moderar usuários
- `moderate:reports` - Gerenciar denúncias

#### **🔐 Permissões**
- `permissions:create` - Criar permissões
- `permissions:read` - Visualizar permissões
- `permissions:update` - Atualizar permissões
- `permissions:delete` - Deletar permissões
- `permissions:assign` - Atribuir permissões

## 🚀 Como Usar

### **1. Proteger Endpoints com Roles**

```typescript
@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard)
export class AdminController {
  
  @Get('dashboard')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  getDashboard() {
    return 'Dashboard administrativo';
  }
}
```

### **2. Proteger Endpoints com Permissões Específicas**

```typescript
@Controller('users')
@UseGuards(JwtAuthGuard, PermissionsGuard)
export class UsersController {
  
  @Post()
  @RequirePermissions(Permission.USERS_CREATE)
  createUser() {
    return 'Usuário criado';
  }
  
  @Delete(':id')
  @RequirePermissions(Permission.USERS_DELETE, Permission.ADMIN_DASHBOARD)
  deleteUser() {
    return 'Usuário deletado';
  }
}
```

### **3. Combinar Guards**

```typescript
@Controller('works')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
export class WorksController {
  
  @Post()
  @Roles(UserRole.ADMIN)
  @RequirePermissions(Permission.WORKS_CREATE)
  createWork() {
    // Usuário deve ser ADMIN E ter permissão works:create
    return 'Obra criada';
  }
}
```

## 🔧 Gerenciamento via API

### **Endpoints Disponíveis**

#### **Roles**
- `POST /permissions/roles` - Criar novo role
- `GET /permissions/roles` - Listar roles
- `POST /permissions/assign-role` - Atribuir role a usuário

#### **Permissões**
- `GET /permissions/users/:userId/permissions` - Listar permissões do usuário

### **Exemplos de Uso**

#### **Criar Role Customizado**
```bash
POST /permissions/roles
{
  "name": "content_editor",
  "description": "Editor de conteúdo especializado",
  "permissionIds": ["uuid-works-create", "uuid-works-update"]
}
```

#### **Atribuir Role Temporário**
```bash
POST /permissions/assign-role
{
  "userId": 123,
  "roleId": "uuid-role-moderator",
  "expiresAt": "2024-12-31T23:59:59.000Z"
}
```

## 🗄️ Estrutura do Banco de Dados

### **Tabelas Principais**

1. **users** - Usuários com role básico
2. **roles** - Roles do sistema
3. **permissions** - Permissões granulares
4. **user_roles** - Relação usuário-role (many-to-many)
5. **role_permissions** - Relação role-permissão (many-to-many)

### **Migrations**

- `002-add-permissions-system.ts` - Cria estrutura de permissões
- `003-seed-permissions-and-roles.ts` - Popula dados iniciais

## 🌱 Inicialização

### **Executar Migrations**
```bash
npm run migration:run
```

### **Executar Seed de Permissões**
```bash
npm run seed:permissions
```

## 🔍 Verificação de Permissões

### **No Código**
```typescript
// Verificar se usuário tem permissão específica
const hasPermission = await userRoleRepository.hasPermission(
  userId, 
  Permission.USERS_CREATE
);

// Listar todas as permissões do usuário
const permissions = await userRoleRepository.getUserPermissions(userId);
```

### **Via API**
```bash
GET /permissions/users/123/permissions
```

## 🛡️ Segurança

### **Boas Práticas Implementadas**

1. **Princípio do Menor Privilégio** - Usuários recebem apenas permissões necessárias
2. **Roles Hierárquicos** - ADMIN herda permissões de MODERATOR, etc.
3. **Roles Temporários** - Suporte a expiração automática
4. **Auditoria** - Registro de quem atribuiu cada role
5. **Validação Dupla** - Guards verificam tanto JWT quanto permissões

### **Proteções Implementadas**

- Roles do sistema não podem ser deletados
- Verificação de existência antes de atribuições
- Prevenção de duplicação de roles
- Logs detalhados de todas as operações

## 📊 Monitoramento

### **Logs Disponíveis**

- Criação/atualização de roles
- Atribuição/remoção de permissões
- Tentativas de acesso negado
- Operações administrativas

### **Métricas Sugeridas**

- Número de usuários por role
- Permissões mais utilizadas
- Tentativas de acesso negado
- Roles temporários próximos ao vencimento

## 🔄 Manutenção

### **Limpeza Automática**

O sistema inclui métodos para:
- Remover roles expirados
- Identificar roles não utilizados
- Auditar permissões órfãs

### **Comandos Úteis**

```typescript
// Remover roles expirados
await userRoleRepository.removeExpiredRoles();

// Listar roles expirados
const expired = await userRoleRepository.findExpiredRoles();
```

---

## 📝 Próximos Passos

1. **Interface Administrativa** - Dashboard para gerenciar permissões
2. **Auditoria Avançada** - Log detalhado de todas as operações
3. **Roles Dinâmicos** - Criação de roles baseados em contexto
4. **Integração com Cache** - Redis para otimizar verificações
5. **Notificações** - Alertas para roles próximos ao vencimento
