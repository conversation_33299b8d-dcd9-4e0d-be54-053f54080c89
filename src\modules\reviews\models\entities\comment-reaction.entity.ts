import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn, Index } from "typeorm";
import { ReviewComment } from "./review-comment.entity";

@Entity("comment_reactions")
@Index(["commentId", "userId"], { unique: true }) // Um usuário só pode reagir uma vez a cada comentário
export class CommentReaction {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "uuid" })
	@Index()
	commentId: string;

	@Column({ type: "integer" })
	@Index()
	userId: number;

	@Column({ type: "boolean" })
	isLike: boolean;

	@ManyToOne(() => ReviewComment, comment => comment.reactions, { onDelete: "CASCADE" })
	@JoinColumn({ name: "commentId" })
	comment: ReviewComment;

	@CreateDateColumn()
	createdAt: Date;
}
