import { Injectable, Inject } from "@nestjs/common";
import { ResourceNotFoundException, ValidationException, UnauthorizedOperationException } from "src/shared/exceptions/business.exceptions";
import { IAssociateGalleryImagesRequest, IWorkRepository } from "../../models/interfaces";
import { IImageRepository } from "../../../images/models/interfaces";
import { UserRole } from "../../../user/models/enums";
import { ImageAssociationResponseDto } from "../../models/dtos";

/**
 * Use Case para associar múltiplas imagens à galeria de uma obra
 * Seguindo princípios SOLID - Single Responsibility
 */
@Injectable()
export class AssociateGalleryImagesUseCase {
	constructor(
		@Inject("IWorkRepository")
		private readonly workRepository: IWorkRepository,
		@Inject("IImageRepository")
		private readonly imageRepository: IImageRepository
	) {}

	async execute(data: IAssociateGalleryImagesRequest, userRole?: UserRole): Promise<ImageAssociationResponseDto> {
		const { workId, imageIds, userId } = data;

		// 1. Verificar se a obra existe
		const work = await this.workRepository.findById(workId);
		if (!work) {
			throw new ResourceNotFoundException("Obra", workId);
		}

		// 2. Validar se há imagens para associar
		if (!imageIds || imageIds.length === 0) {
			throw new ValidationException({
				imageIds: ["Pelo menos uma imagem deve ser fornecida"],
			});
		}

		// 3. Verificar se todas as imagens existem e estão ativas
		const images = await Promise.all(imageIds.map(id => this.imageRepository.findById(id)));

		const notFoundImages = imageIds.filter((id, index) => !images[index]);
		if (notFoundImages.length > 0) {
			throw new ValidationException({
				imageIds: [`Imagens não encontradas: ${notFoundImages.join(", ")}`],
			});
		}

		const unavailableImages = images
			.map((img, index) => ({ img, id: imageIds[index] }))
			.filter(({ img }) => img && !img.isAvailable())
			.map(({ id }) => id);

		if (unavailableImages.length > 0) {
			throw new ValidationException({
				imageIds: [`Imagens não disponíveis: ${unavailableImages.join(", ")}`],
			});
		}

		// 4. Validar propriedade das imagens (apenas proprietário ou admin pode associar)
		const isAdmin = userRole === UserRole.ADMIN;

		if (!isAdmin) {
			const unauthorizedImages = images
				.map((img, index) => ({ img, id: imageIds[index] }))
				.filter(({ img }) => img && img.uploadedBy !== userId)
				.map(({ id }) => id);

			if (unauthorizedImages.length > 0) {
				throw new UnauthorizedOperationException(`Você não tem permissão para usar as imagens: ${unauthorizedImages.join(", ")}`);
			}
		}

		// 5. Associar as imagens à galeria
		await this.workRepository.associateGalleryImages(workId, imageIds);

		return {
			success: true,
			message: `${imageIds.length} imagem(ns) associada(s) à galeria com sucesso`,
			workId,
			imageIds,
		};
	}
}
