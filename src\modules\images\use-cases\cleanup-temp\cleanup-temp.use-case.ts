import { Inject, Injectable } from "@nestjs/common";
import { BaseUseCase } from "../../../../shared/use-cases/base.use-case";
import { IImageRepository } from "../../models/interfaces/image-repository.interface";
import * as fs from "fs-extra";
import * as path from "path";

/**
 * Interface para configuração de limpeza
 */
export interface ICleanupConfig {
	tempFileAgeMinutes?: number;
	cleanupOrphanedFiles?: boolean;
	cleanupDeletedImages?: boolean;
	dryRun?: boolean;
}

/**
 * Interface para resultado de limpeza
 */
export interface ICleanupResult {
	success: boolean;
	tempFilesRemoved: number;
	orphanedFilesRemoved: number;
	deletedImagesRemoved: number;
	totalFilesRemoved: number;
	totalSpaceFreed: number;
	errors: string[];
	executionTime: number;
}

/**
 * Use Case para limpeza automática de arquivos temporários
 * Seguindo princípios SOLID - Single Responsibility
 */
@Injectable()
export class CleanupTempUseCase extends BaseUseCase<ICleanupConfig, ICleanupResult> {
	private readonly baseUploadPath: string;
	private readonly tempPath: string;

	constructor(
		@Inject("IImageRepository")
		private readonly imageRepository: IImageRepository
	) {
		super("CleanupTempUseCase");
		this.baseUploadPath = path.join(process.cwd(), "uploads", "images");
		this.tempPath = path.join(process.cwd(), "uploads", "temp");
	}

	async execute(config: ICleanupConfig = {}): Promise<ICleanupResult> {
		return this.executeWithLogging("CleanupTemp", config, async () => {
			const startTime = Date.now();
			const errors: string[] = [];

			const {
				tempFileAgeMinutes = 60, // 1 hora por padrão
				cleanupOrphanedFiles = true,
				cleanupDeletedImages = true,
				dryRun = false,
			} = config;

			let tempFilesRemoved = 0;
			let orphanedFilesRemoved = 0;
			let deletedImagesRemoved = 0;
			const totalSpaceFreed = 0;

			try {
				// 1. Limpar arquivos temporários antigos
				if (!dryRun) {
					tempFilesRemoved = await this.cleanupTempFiles(tempFileAgeMinutes);
				} else {
					// Em modo dry run, apenas contar
					const tempImages = await this.imageRepository.findTempImages(tempFileAgeMinutes);
					tempFilesRemoved = tempImages.length;
				}

				// 2. Limpar arquivos órfãos (se habilitado)
				if (cleanupOrphanedFiles) {
					try {
						if (!dryRun) {
							orphanedFilesRemoved = await this.cleanupOrphanedFiles();
						} else {
							// Simular contagem de órfãos
							orphanedFilesRemoved = 0; // Implementar contagem se necessário
						}
					} catch (error) {
						errors.push(`Erro ao limpar arquivos órfãos: ${error.message}`);
					}
				}

				// 3. Limpar imagens marcadas como deletadas (se habilitado)
				if (cleanupDeletedImages) {
					try {
						if (!dryRun) {
							deletedImagesRemoved = await this.cleanupDeletedImages();
						} else {
							// Contar imagens deletadas
							const deletedCount = await this.imageRepository.countByStatus("deleted" as any);
							deletedImagesRemoved = deletedCount;
						}
					} catch (error) {
						errors.push(`Erro ao limpar imagens deletadas: ${error.message}`);
					}
				}
			} catch (error) {
				errors.push(`Erro geral na limpeza: ${error.message}`);
			}

			const totalFilesRemoved = tempFilesRemoved + orphanedFilesRemoved + deletedImagesRemoved;
			const executionTime = Date.now() - startTime;

			return {
				success: errors.length === 0,
				tempFilesRemoved,
				orphanedFilesRemoved,
				deletedImagesRemoved,
				totalFilesRemoved,
				totalSpaceFreed,
				errors,
				executionTime,
			};
		});
	}

	// ========== MÉTODOS UTILITÁRIOS ==========

	/**
	 * Limpa arquivos temporários antigos
	 */
	private async cleanupTempFiles(ageMinutes: number): Promise<number> {
		const tempImages = await this.imageRepository.findTempImages(ageMinutes);
		let removedCount = 0;

		for (const image of tempImages) {
			try {
				const fullPath = path.join(this.baseUploadPath, image.path);
				const fileExists = await this.fileExists(fullPath);

				if (fileExists) {
					await fs.remove(fullPath);
				}

				// Remover do banco
				await this.imageRepository.hardDelete(image.id);
				removedCount++;
			} catch (error) {
				// Continuar com próximo arquivo em caso de erro
				console.error(`Erro ao limpar arquivo temporário ${image.path}:`, error);
			}
		}

		return removedCount;
	}

	/**
	 * Limpa arquivos órfãos (arquivos sem registro no banco)
	 */
	private async cleanupOrphanedFiles(): Promise<number> {
		// Implementação básica - pode ser expandida
		return 0;
	}

	/**
	 * Limpa imagens deletadas permanentemente
	 */
	private async cleanupDeletedImages(): Promise<number> {
		// Buscar imagens com status deleted
		const deletedImages = await this.imageRepository.findByStatus("deleted" as any);
		let removedCount = 0;

		for (const image of deletedImages) {
			try {
				const fullPath = path.join(this.baseUploadPath, image.path);
				const fileExists = await this.fileExists(fullPath);

				if (fileExists) {
					await fs.remove(fullPath);
				}

				// Remover do banco
				await this.imageRepository.hardDelete(image.id);
				removedCount++;
			} catch (error) {
				console.error(`Erro ao limpar imagem deletada ${image.path}:`, error);
			}
		}

		return removedCount;
	}

	/**
	 * Verifica se arquivo existe
	 */
	private async fileExists(filePath: string): Promise<boolean> {
		try {
			await fs.access(filePath);
			return true;
		} catch {
			return false;
		}
	}
}

/**
 * Use Case para limpeza completa do sistema
 */
@Injectable()
export class FullCleanupUseCase extends BaseUseCase<ICleanupConfig, ICleanupResult> {
	private readonly baseUploadPath: string;
	private readonly tempPath: string;

	constructor(
		@Inject("IImageRepository")
		private readonly imageRepository: IImageRepository
	) {
		super("FullCleanupUseCase");
		this.baseUploadPath = path.join(process.cwd(), "uploads", "images");
		this.tempPath = path.join(process.cwd(), "uploads", "temp");
	}

	async execute(config: ICleanupConfig = {}): Promise<ICleanupResult> {
		return this.executeWithLogging("FullCleanup", config, async () => {
			const startTime = Date.now();
			const errors: string[] = [];

			const { dryRun = false } = config;

			let result = {
				tempFilesRemoved: 0,
				orphanedFilesRemoved: 0,
				deletedImagesRemoved: 0,
			};

			try {
				if (!dryRun) {
					result = await this.performFullCleanup();
				} else {
					// Em modo dry run, simular limpeza
					// Implementar contagem sem executar limpeza
				}
			} catch (error) {
				errors.push(`Erro na limpeza completa: ${error.message}`);
			}

			const totalFilesRemoved = result.tempFilesRemoved + result.orphanedFilesRemoved + result.deletedImagesRemoved;
			const executionTime = Date.now() - startTime;

			return {
				success: errors.length === 0,
				tempFilesRemoved: result.tempFilesRemoved,
				orphanedFilesRemoved: result.orphanedFilesRemoved,
				deletedImagesRemoved: result.deletedImagesRemoved,
				totalFilesRemoved,
				totalSpaceFreed: 0, // Implementar cálculo se necessário
				errors,
				executionTime,
			};
		});
	}

	// ========== MÉTODOS UTILITÁRIOS ==========

	/**
	 * Executa limpeza completa do sistema
	 */
	private async performFullCleanup(): Promise<{
		tempFilesRemoved: number;
		orphanedFilesRemoved: number;
		deletedImagesRemoved: number;
	}> {
		// Implementação básica - pode ser expandida
		return {
			tempFilesRemoved: 0,
			orphanedFilesRemoved: 0,
			deletedImagesRemoved: 0,
		};
	}
}

/**
 * Use Case para obter estatísticas de limpeza
 */
@Injectable()
export class CleanupStatsUseCase extends BaseUseCase<void, any> {
	constructor(
		@Inject("IImageRepository")
		private readonly imageRepository: IImageRepository
	) {
		super("CleanupStatsUseCase");
	}

	async execute(): Promise<{
		tempImagesCount: number;
		deletedImagesCount: number;
		totalImagesCount: number;
		estimatedCleanupSize: number;
	}> {
		return this.executeWithLogging("CleanupStats", null, async () => {
			// Contar imagens temporárias (mais de 1 hora)
			const tempImages = await this.imageRepository.findTempImages(60);

			// Contar imagens deletadas
			const deletedImagesCount = await this.imageRepository.countByStatus("deleted" as any);

			// Contar total de imagens
			const totalImagesCount =
				(await this.imageRepository.countByStatus("active" as any)) +
				(await this.imageRepository.countByStatus("processing" as any)) +
				(await this.imageRepository.countByStatus("uploading" as any)) +
				deletedImagesCount;

			// Estimar tamanho de limpeza
			const estimatedCleanupSize = tempImages.reduce((total, img) => total + Number(img.size), 0);

			return {
				tempImagesCount: tempImages.length,
				deletedImagesCount,
				totalImagesCount,
				estimatedCleanupSize,
			};
		});
	}
}
