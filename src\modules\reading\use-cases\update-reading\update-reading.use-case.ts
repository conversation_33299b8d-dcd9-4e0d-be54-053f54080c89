import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IUpdateUserWorkRequest, IUserWork, IUserWorkRepository } from "../../models/interfaces";

@Injectable()
export class UpdateReadingUseCase {
	constructor(
		@Inject("IUserWorkRepository")
		private readonly userWorkRepository: IUserWorkRepository
	) {}

	async execute(userId: string, userWorkId: string, data: IUpdateUserWorkRequest): Promise<IUserWork> {
		// Verificar se o registro existe e pertence ao usuário
		const userWork = await this.userWorkRepository.findById(userWorkId);
		if (!userWork) {
			throw new ResourceNotFoundException("Registro de leitura", userWorkId);
		}
		if (userWork.userId !== Number(userId)) {
			throw new ResourceNotFoundException("Registro de leitura do usuário", `${userWorkId}:${userId}`);
		}

		return await this.userWorkRepository.update(userWorkId, data);
	}
}
