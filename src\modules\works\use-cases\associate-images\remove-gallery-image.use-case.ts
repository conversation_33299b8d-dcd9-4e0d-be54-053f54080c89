import { Injectable, Inject } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IWorkRepository } from "../../models/interfaces";
import { ImageAssociationResponseDto } from "../../models/dtos";

/**
 * Use Case para remover imagem específica da galeria de uma obra
 * Seguindo princípios SOLID - Single Responsibility
 */
@Injectable()
export class RemoveGalleryImageUseCase {
	constructor(
		@Inject("IWorkRepository")
		private readonly workRepository: IWorkRepository
	) {}

	async execute(workId: string, imageId: number): Promise<ImageAssociationResponseDto> {
		// 1. Verificar se a obra existe
		const work = await this.workRepository.findById(workId);
		if (!work) {
			throw new ResourceNotFoundException("Obra", workId);
		}

		await this.workRepository.removeGalleryImage(workId, imageId);

		return {
			success: true,
			message: "Imagem removida da galeria com sucesso",
			workId,
			imageIds: [imageId],
		};
	}
}
