import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { RankingItemFiltersDto } from "../../models/dtos";
import { IRankingItem, IRankingItemRepository, IRankingRepository } from "../../models/interfaces";

@Injectable()
export class GetRankingItemsUseCase {
	constructor(
		@Inject("IRankingRepository")
		private rankingRepository: IRankingRepository,

		@Inject("IRankingItemRepository")
		private rankingItemRepository: IRankingItemRepository
	) {}

	async execute(rankingId: string, userId: string | null, filters?: RankingItemFiltersDto): Promise<IRankingItem[]> {
		// Verificar se o ranking existe e se é acessível ao usuário
		const ranking = await this.rankingRepository.findById(rankingId);
		if (!ranking) {
			throw new ResourceNotFoundException("Ranking", rankingId);
		}
		// Se o ranking não for público e não pertencer ao usuário, não permita acesso
		if (!ranking.isPublic && ranking.userId !== Number(userId)) {
			throw new ResourceNotFoundException("Ranking privado", `${rankingId}:${userId}`);
		}

		// Buscar os itens do ranking com filtros aplicados
		let parsedFilters: any = filters;
		if (filters) {
			parsedFilters = {
				...filters,
				sortBy: (["position", "rating", "createdAt"].includes(filters.sortBy as string) ? filters.sortBy : undefined) as
					| "position"
					| "rating"
					| "createdAt"
					| undefined,
				sortOrder: (["ASC", "DESC"].includes((filters.sortOrder as string)?.toUpperCase())
					? (filters.sortOrder as string).toUpperCase()
					: undefined) as "ASC" | "DESC" | undefined,
			};
		}
		return await this.rankingItemRepository.findByRankingId(rankingId, parsedFilters);
	}
}
