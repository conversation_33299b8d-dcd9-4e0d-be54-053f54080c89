import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, In } from "typeorm";
import { Role, PermissionEntity } from "../models/entities";
import { IRoleRepository } from "../models/interfaces";
import { Permission } from "../models/enums";

@Injectable()
export class RoleTypeOrmRepository implements IRoleRepository {
	constructor(
		@InjectRepository(Role)
		private readonly roleRepository: Repository<Role>,
		@InjectRepository(PermissionEntity)
		private readonly permissionRepository: Repository<PermissionEntity>
	) {}

	async findById(id: string): Promise<Role | null> {
		return this.roleRepository.findOne({ where: { id } });
	}

	async findByName(name: string): Promise<Role | null> {
		return this.roleRepository.findOne({ where: { name } });
	}

	async findAll(): Promise<Role[]> {
		return this.roleRepository.find({
			where: { isActive: true },
			order: { name: "ASC" },
		});
	}

	async findAllWithPermissions(): Promise<Role[]> {
		return this.roleRepository.find({
			where: { isActive: true },
			relations: ["permissions"],
			order: { name: "ASC" },
		});
	}

	async findByIdWithPermissions(id: string): Promise<Role | null> {
		return this.roleRepository.findOne({
			where: { id },
			relations: ["permissions"],
		});
	}

	async create(roleData: Partial<Role>): Promise<Role> {
		const role = this.roleRepository.create(roleData);
		return this.roleRepository.save(role);
	}

	async update(id: string, roleData: Partial<Role>): Promise<Role> {
		await this.roleRepository.update(id, roleData);
		return this.findById(id);
	}

	async delete(id: string): Promise<void> {
		await this.roleRepository.update(id, { isActive: false });
	}

	async addPermissions(roleId: string, permissionIds: string[]): Promise<void> {
		const role = await this.roleRepository.findOne({
			where: { id: roleId },
			relations: ["permissions"],
		});

		if (!role) {
			throw new Error("Role não encontrado");
		}

		const permissions = await this.permissionRepository.find({
			where: { id: In(permissionIds) },
		});

		// Adiciona apenas permissões que ainda não estão associadas
		const existingPermissionIds = role.permissions.map(p => p.id);
		const newPermissions = permissions.filter(p => !existingPermissionIds.includes(p.id));

		role.permissions = [...role.permissions, ...newPermissions];
		await this.roleRepository.save(role);
	}

	async removePermissions(roleId: string, permissionIds: string[]): Promise<void> {
		const role = await this.roleRepository.findOne({
			where: { id: roleId },
			relations: ["permissions"],
		});

		if (!role) {
			throw new Error("Role não encontrado");
		}

		role.permissions = role.permissions.filter(p => !permissionIds.includes(p.id));
		await this.roleRepository.save(role);
	}

	async hasPermission(roleId: string, permission: Permission): Promise<boolean> {
		const role = await this.roleRepository.findOne({
			where: { id: roleId },
			relations: ["permissions"],
		});

		if (!role) {
			return false;
		}

		return role.permissions.some(p => p.name === permission);
	}

	async getPermissions(roleId: string): Promise<Permission[]> {
		const role = await this.roleRepository.findOne({
			where: { id: roleId },
			relations: ["permissions"],
		});

		if (!role) {
			return [];
		}

		return role.permissions.map(p => p.name);
	}
}
