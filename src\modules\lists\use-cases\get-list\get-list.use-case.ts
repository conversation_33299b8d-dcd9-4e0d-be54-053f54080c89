import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IList, IListRepository } from "../../models/interfaces";

@Injectable()
export class GetListUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository
	) {}

	async execute(userId: number, listId: string): Promise<IList> {
		const list = await this.listRepository.findById(listId);
		if (!list) {
			throw new ResourceNotFoundException("Lista", listId);
		}
		// Se a lista não é pública, verificar se pertence ao usuário
		if (!list.isPublic && list.userId !== userId) {
			throw new ResourceNotFoundException("Lista privada", `${listId}:${userId}`);
		}

		return list;
	}
}
