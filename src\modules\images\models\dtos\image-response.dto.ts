import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { ImageStatus, ImageType } from "../enums";

/**
 * DTO de resposta para imagem
 * Seguindo princípios SOLID - Single Responsibility
 */
export class ImageResponseDto {
	@ApiProperty({
		description: "ID único da imagem",
		example: 1,
	})
	id: number;

	@ApiProperty({
		description: "Nome do arquivo gerado pelo sistema",
		example: "img_1640995200000_abc123.jpg",
	})
	filename: string;

	@ApiProperty({
		description: "Nome original do arquivo enviado",
		example: "minha-foto.jpg",
	})
	originalName: string;

	@ApiProperty({
		description: "Caminho relativo do arquivo",
		example: "images/2024/06/img_1640995200000_abc123.jpg",
	})
	path: string;

	@ApiProperty({
		description: "URL completa para acesso à imagem",
		example: "http://localhost:3000/api/images/1",
	})
	url: string;

	@ApiProperty({
		description: "Tipo MIME da imagem",
		enum: ImageType,
		example: ImageType.JPEG,
	})
	mimeType: ImageType;

	@ApiProperty({
		description: "Tamanho do arquivo em bytes",
		example: 1024000,
	})
	size: number;

	@ApiProperty({
		description: "Tamanho formatado para exibição",
		example: "1.00 MB",
	})
	formattedSize: string;

	@ApiPropertyOptional({
		description: "Largura da imagem em pixels",
		example: 1920,
	})
	width?: number;

	@ApiPropertyOptional({
		description: "Altura da imagem em pixels",
		example: 1080,
	})
	height?: number;

	@ApiPropertyOptional({
		description: "Dimensões formatadas",
		example: "1920x1080",
	})
	dimensions?: string;

	@ApiProperty({
		description: "Status atual da imagem",
		enum: ImageStatus,
		example: ImageStatus.ACTIVE,
	})
	status: ImageStatus;

	@ApiPropertyOptional({
		description: "Hash da imagem para detecção de duplicatas",
		example: "a1b2c3d4e5f6",
	})
	hash?: string;

	@ApiPropertyOptional({
		description: "Metadados adicionais da imagem",
		example: '{"camera":"Canon EOS R5","iso":100}',
	})
	metadata?: string;

	@ApiPropertyOptional({
		description: "ID do usuário que fez o upload",
		example: 1,
	})
	uploadedBy?: number;

	@ApiProperty({
		description: "Data de criação",
		example: "2024-06-12T10:30:00.000Z",
	})
	createdAt: Date;

	@ApiProperty({
		description: "Data de última atualização",
		example: "2024-06-12T10:30:00.000Z",
	})
	updatedAt: Date;

	@ApiPropertyOptional({
		description: "Data de exclusão (se aplicável)",
		example: null,
	})
	deletedAt?: Date;
}

/**
 * DTO de resposta para lista paginada de imagens
 */
export class ImageListResponseDto {
	@ApiProperty({
		description: "Lista de imagens",
		type: [ImageResponseDto],
	})
	images: ImageResponseDto[];

	@ApiProperty({
		description: "Total de imagens",
		example: 150,
	})
	total: number;

	@ApiProperty({
		description: "Total de páginas",
		example: 15,
	})
	totalPages: number;

	@ApiProperty({
		description: "Página atual",
		example: 1,
	})
	currentPage: number;

	@ApiProperty({
		description: "Itens por página",
		example: 10,
	})
	itemsPerPage: number;
}

/**
 * DTO de resposta para estatísticas de imagens
 */
export class ImageStatsResponseDto {
	@ApiProperty({
		description: "Total de imagens ativas",
		example: 120,
	})
	totalActive: number;

	@ApiProperty({
		description: "Total de imagens em processamento",
		example: 5,
	})
	totalProcessing: number;

	@ApiProperty({
		description: "Total de imagens deletadas",
		example: 25,
	})
	totalDeleted: number;

	@ApiProperty({
		description: "Espaço total usado em bytes",
		example: 104857600,
	})
	totalSizeBytes: number;

	@ApiProperty({
		description: "Espaço total usado formatado",
		example: "100.00 MB",
	})
	totalSizeFormatted: string;

	@ApiProperty({
		description: "Espaço médio por imagem em bytes",
		example: 873813,
	})
	averageSizeBytes: number;

	@ApiProperty({
		description: "Espaço médio por imagem formatado",
		example: "853.33 KB",
	})
	averageSizeFormatted: string;
}
