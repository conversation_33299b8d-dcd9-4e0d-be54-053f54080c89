import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString } from "class-validator";

export class RefreshTokenDto {
	@ApiProperty({
		description: "Token de refresh para renovar o access token",
		example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
		type: "string",
		format: "jwt",
	})
	@IsNotEmpty({ message: "Refresh token é obrigatório" })
	@IsString({ message: "Refresh token deve ser uma string" })
	refresh_token: string;
}
