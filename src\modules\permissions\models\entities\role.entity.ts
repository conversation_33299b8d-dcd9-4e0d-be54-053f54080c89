import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToMany, JoinTable, Index } from "typeorm";
import { PermissionEntity } from "./permission.entity";

/**
 * Entidade que representa um role/função no sistema
 *
 * @entity roles
 */
@Entity("roles")
export class Role {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ unique: true, length: 50 })
	@Index()
	name: string;

	@Column({ length: 255, nullable: true })
	description?: string;

	@Column({ default: true })
	isActive: boolean;

	@Column({ default: false })
	isSystem: boolean; // Indica se é um role do sistema (não pode ser deletado)

	@ManyToMany(() => PermissionEntity, permission => permission.roles, {
		cascade: true,
		eager: false,
	})
	@JoinTable({
		name: "role_permissions",
		joinColumn: {
			name: "roleId",
			referencedColumnName: "id",
		},
		inverseJoinColumn: {
			name: "permissionId",
			referencedColumnName: "id",
		},
	})
	permissions: PermissionEntity[];

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;
}
