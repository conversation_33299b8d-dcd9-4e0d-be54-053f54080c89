import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString, IsN<PERSON>ber, Min, Max, IsBoolean } from "class-validator";

/**
 * DTO para upload de imagem
 * Seguindo princípios SOLID - Single Responsibility
 */
export class UploadImageDto {
	@ApiPropertyOptional({
		description: "Descrição opcional da imagem",
		example: "Avatar do usuário",
		maxLength: 500,
	})
	@IsOptional()
	@IsString()
	description?: string;

	@ApiPropertyOptional({
		description: "Tags associadas à imagem (separadas por vírgula)",
		example: "avatar,perfil,usuario",
		maxLength: 200,
	})
	@IsOptional()
	@IsString()
	tags?: string;

	@ApiPropertyOptional({
		description: "Largura máxima desejada para redimensionamento",
		example: 800,
		minimum: 50,
		maximum: 4000,
	})
	@IsOptional()
	@IsNumber()
	@Min(50)
	@Max(4000)
	maxWidth?: number;

	@ApiPropertyOptional({
		description: "Altura máxima desejada para redimensionamento",
		example: 600,
		minimum: 50,
		maximum: 4000,
	})
	@IsOptional()
	@IsNumber()
	@Min(50)
	@Max(4000)
	maxHeight?: number;

	@ApiPropertyOptional({
		description: "Qualidade da imagem (1-100)",
		example: 85,
		minimum: 1,
		maximum: 100,
	})
	@IsOptional()
	@IsNumber()
	@Min(1)
	@Max(100)
	quality?: number;

	@ApiPropertyOptional({
		description: "Se deve otimizar a imagem para web",
		example: true,
		default: true,
	})
	@IsOptional()
	@IsBoolean()
	optimize?: boolean = true;

	@ApiPropertyOptional({
		description: "Se deve gerar thumbnail automaticamente",
		example: true,
		default: false,
	})
	@IsOptional()
	@IsBoolean()
	generateThumbnail?: boolean = false;
}

/**
 * DTO para configurações de upload
 */
export class UploadConfigDto {
	@ApiProperty({
		description: "Tamanho máximo do arquivo em MB",
		example: 5,
		minimum: 1,
		maximum: 50,
	})
	@IsNumber()
	@Min(1)
	@Max(50)
	maxSizeInMB: number = 5;

	@ApiProperty({
		description: "Tipos de arquivo permitidos",
		example: ["image/jpeg", "image/png", "image/webp"],
		isArray: true,
	})
	allowedMimeTypes: string[] = ["image/jpeg", "image/jpg", "image/png", "image/webp"];

	@ApiProperty({
		description: "Largura máxima permitida",
		example: 2000,
		minimum: 100,
		maximum: 8000,
	})
	@IsNumber()
	@Min(100)
	@Max(8000)
	maxWidth: number = 2000;

	@ApiProperty({
		description: "Altura máxima permitida",
		example: 2000,
		minimum: 100,
		maximum: 8000,
	})
	@IsNumber()
	@Min(100)
	@Max(8000)
	maxHeight: number = 2000;
}
