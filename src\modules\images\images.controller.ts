import {
	Controller,
	Post,
	Get,
	Delete,
	Param,
	Query,
	UseInterceptors,
	UploadedFile,
	UseGuards,
	Req,
	Res,
	HttpStatus,
	Body,
	ParseIntPipe,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBearerAuth } from "@nestjs/swagger";
import { Response, Request } from "express";
import { JwtAuthGuard } from "../auth/models/guard/jwt-auth.guard";
import { RolesGuard } from "../permissions/models/guards/roles.guard";
import { Roles } from "../permissions/models/decorators/roles.decorator";
import { UserRole } from "../user/models/enums/user-role.enum";

// Use Cases
import { UploadImageUseCase } from "./use-cases/upload-image/upload-image.use-case";
import { GetImageUseCase, ListImagesUseCase } from "./use-cases/get-image/get-image.use-case";
import { DeleteImageUseCase, RestoreImageUseCase } from "./use-cases/delete-image/delete-image.use-case";
import { CleanupTempUseCase, CleanupStatsUseCase } from "./use-cases/cleanup-temp/cleanup-temp.use-case";

// DTOs
import { UploadImageDto, ImageResponseDto, ImageFilterDto } from "./models/dtos";

// Middlewares
import { MulterConfigFactory } from "./middlewares/multer-config.middleware";

// Exceptions
import { ImageNotFoundException, ImagePermissionException } from "./exceptions";

/**
 * Controller para gerenciamento de imagens
 * Seguindo princípios SOLID - Single Responsibility
 */
@ApiTags("Images")
@Controller("images")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ImagesController {
	constructor(
		private readonly uploadImageUseCase: UploadImageUseCase,
		private readonly getImageUseCase: GetImageUseCase,
		private readonly listImagesUseCase: ListImagesUseCase,
		private readonly deleteImageUseCase: DeleteImageUseCase,
		private readonly restoreImageUseCase: RestoreImageUseCase,
		private readonly cleanupTempUseCase: CleanupTempUseCase,
		private readonly cleanupStatsUseCase: CleanupStatsUseCase
	) {}

	@Post("upload")
	@ApiOperation({ summary: "Upload de imagem" })
	@ApiConsumes("multipart/form-data")
	@ApiResponse({ status: 201, description: "Imagem enviada com sucesso", type: ImageResponseDto })
	@ApiResponse({ status: 400, description: "Dados inválidos" })
	@UseInterceptors(FileInterceptor("file", MulterConfigFactory.forContent()))
	async uploadImage(@UploadedFile() file: Express.Multer.File, @Body() uploadData: UploadImageDto, @Req() req: Request): Promise<ImageResponseDto> {
		const userId = req.user?.["id"];

		return await this.uploadImageUseCase.execute({
			file,
			uploadData,
			uploaderId: userId,
		});
	}

	@Post("upload/avatar")
	@ApiOperation({ summary: "Upload de avatar" })
	@ApiConsumes("multipart/form-data")
	@ApiResponse({ status: 201, description: "Avatar enviado com sucesso", type: ImageResponseDto })
	@UseInterceptors(FileInterceptor("file", MulterConfigFactory.forAvatars()))
	async uploadAvatar(@UploadedFile() file: Express.Multer.File, @Req() req: Request): Promise<ImageResponseDto> {
		const userId = req.user?.["id"];

		const uploadData: UploadImageDto = {
			maxWidth: 300,
			maxHeight: 300,
			optimize: true,
			quality: 90,
		};

		return await this.uploadImageUseCase.execute({
			file,
			uploadData,
			uploaderId: userId,
		});
	}

	@Get()
	@ApiOperation({ summary: "Listar imagens" })
	@ApiResponse({ status: 200, description: "Lista de imagens" })
	async listImages(@Query() filters: ImageFilterDto): Promise<any> {
		return await this.listImagesUseCase.execute(filters);
	}

	@Get("my")
	@ApiOperation({ summary: "Listar minhas imagens" })
	@ApiResponse({ status: 200, description: "Lista das imagens do usuário" })
	async listMyImages(@Query() filters: ImageFilterDto, @Req() req: Request): Promise<any> {
		const userId = req.user?.["id"];

		return await this.listImagesUseCase.execute({
			...filters,
			uploaderId: userId,
		});
	}

	@Get(":id")
	@ApiOperation({ summary: "Obter dados da imagem" })
	@ApiResponse({ status: 200, description: "Dados da imagem", type: ImageResponseDto })
	@ApiResponse({ status: 404, description: "Imagem não encontrada" })
	async getImage(@Param("id", ParseIntPipe) id: number): Promise<ImageResponseDto> {
		return await this.getImageUseCase.execute(id);
	}

	@Get(":id/file")
	@ApiOperation({ summary: "Baixar arquivo da imagem" })
	@ApiResponse({ status: 200, description: "Arquivo da imagem" })
	@ApiResponse({ status: 404, description: "Imagem não encontrada" })
	async downloadImage(@Param("id", ParseIntPipe) id: number, @Res() res: Response): Promise<void> {
		const imageFile = await this.getImageUseCase.getImageFile(id);

		res.setHeader("Content-Type", imageFile.mimeType);
		res.setHeader("Content-Disposition", `inline; filename="${imageFile.filename}"`);
		res.setHeader("Content-Length", imageFile.size);

		res.sendFile(imageFile.filePath);
	}

	@Delete(":id")
	@ApiOperation({ summary: "Deletar imagem (soft delete)" })
	@ApiResponse({ status: 200, description: "Imagem deletada com sucesso" })
	@ApiResponse({ status: 404, description: "Imagem não encontrada" })
	async deleteImage(@Param("id", ParseIntPipe) id: number, @Req() req: Request): Promise<any> {
		const userId = req.user?.["id"];

		return await this.deleteImageUseCase.execute({
			imageId: id,
			userId,
			hardDelete: false,
		});
	}

	@Delete(":id/permanent")
	@ApiOperation({ summary: "Deletar imagem permanentemente" })
	@ApiResponse({ status: 200, description: "Imagem deletada permanentemente" })
	@UseGuards(RolesGuard)
	@Roles(UserRole.ADMIN)
	async deleteImagePermanent(@Param("id", ParseIntPipe) id: number, @Req() req: Request): Promise<any> {
		const userId = req.user?.["id"];

		return await this.deleteImageUseCase.execute({
			imageId: id,
			userId,
			hardDelete: true,
		});
	}

	@Post(":id/restore")
	@ApiOperation({ summary: "Restaurar imagem deletada" })
	@ApiResponse({ status: 200, description: "Imagem restaurada com sucesso" })
	@UseGuards(RolesGuard)
	@Roles(UserRole.ADMIN)
	async restoreImage(@Param("id", ParseIntPipe) id: number): Promise<any> {
		return await this.restoreImageUseCase.execute(id);
	}

	@Post("cleanup/temp")
	@ApiOperation({ summary: "Limpeza manual de arquivos temporários" })
	@ApiResponse({ status: 200, description: "Limpeza executada com sucesso" })
	@UseGuards(RolesGuard)
	@Roles(UserRole.ADMIN)
	async cleanupTemp(@Body() config?: any): Promise<any> {
		return await this.cleanupTempUseCase.execute(config);
	}

	@Get("stats/cleanup")
	@ApiOperation({ summary: "Estatísticas de limpeza" })
	@ApiResponse({ status: 200, description: "Estatísticas de limpeza" })
	@UseGuards(RolesGuard)
	@Roles(UserRole.ADMIN)
	async getCleanupStats(): Promise<any> {
		return await this.cleanupStatsUseCase.execute();
	}
}
