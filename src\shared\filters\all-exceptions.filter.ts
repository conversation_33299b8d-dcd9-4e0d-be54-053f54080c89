import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus, Logger } from "@nestjs/common";
import { HttpAdapterHost } from "@nestjs/core";
import { ErrorResponseDto } from "../dtos/error-response.dto";
import { BusinessException } from "../exceptions/business.exceptions";

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
	private readonly logger = new Logger(AllExceptionsFilter.name);

	constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

	catch(exception: unknown, host: ArgumentsHost): void {
		const { httpAdapter } = this.httpAdapterHost;
		const ctx = host.switchToHttp();
		const request = ctx.getRequest();

		let httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
		let message = "Ocorreu um erro interno no servidor. Tente novamente mais tarde.";
		let error = "INTERNAL_SERVER_ERROR";

		console.log("Está aqui", exception);

		if (exception instanceof BusinessException) {
			// Tratamento específico para exceções de negócio
			httpStatus = exception.getStatus();
			const exceptionResponse = exception.getResponse() as any;

			message = exceptionResponse.message;
			error = exceptionResponse.error;

			// Log apenas para exceções internas (500)
			if (httpStatus >= 500) {
				this.logger.error(`Business Error [${error}]:`, {
					message: exceptionResponse.message,
					details: exceptionResponse.details,
					path: request.url,
					method: request.method,
					stack: exception.stack,
				});
			}
		} else if (exception instanceof HttpException) {
			// Tratamento para outras exceções HTTP
			httpStatus = exception.getStatus();
			const exceptionResponse = exception.getResponse();

			if (typeof exceptionResponse === "string") {
				message = this.getUserFriendlyMessage(httpStatus, exceptionResponse);
				error = this.getErrorType(httpStatus);
			} else if (typeof exceptionResponse === "object" && exceptionResponse !== null) {
				const response = exceptionResponse as any;
				message = this.getUserFriendlyMessage(httpStatus, response.message || response.error);
				error = response.error || this.getErrorType(httpStatus);
			}
		} else {
			// Log de exceções não HTTP (erros inesperados)
			this.logger.error("Unexpected error:", {
				error: exception,
				path: request.url,
				method: request.method,
				stack: exception instanceof Error ? exception.stack : "No stack available",
			});
		}

		const errorResponse: ErrorResponseDto = {
			message,
			error,
			statusCode: httpStatus,
			timestamp: new Date().toISOString(),
			path: request.url,
		};

		httpAdapter.reply(ctx.getResponse(), errorResponse, httpStatus);
	}
	private getUserFriendlyMessage(status: HttpStatus, originalMessage?: string | string[]): string {
		// Mapear códigos de status para mensagens amigáveis
		const friendlyMessages: Record<number, string> = {
			[HttpStatus.BAD_REQUEST]: "Os dados fornecidos são inválidos. Verifique as informações e tente novamente.",
			[HttpStatus.UNAUTHORIZED]: "Acesso negado. Você precisa estar autenticado para acessar este recurso.",
			[HttpStatus.FORBIDDEN]: "Você não tem permissão para realizar esta ação.",
			[HttpStatus.NOT_FOUND]: "O recurso solicitado não foi encontrado.",
			[HttpStatus.CONFLICT]: "Conflito detectado. O recurso já existe ou está em uso.",
			[HttpStatus.UNPROCESSABLE_ENTITY]: "Os dados fornecidos não puderam ser processados.",
			[HttpStatus.TOO_MANY_REQUESTS]: "Muitas tentativas. Tente novamente mais tarde.",
			[HttpStatus.INTERNAL_SERVER_ERROR]: "Ocorreu um erro interno no servidor. Tente novamente mais tarde.",
			[HttpStatus.BAD_GATEWAY]: "Serviço temporariamente indisponível. Tente novamente em alguns minutos.",
			[HttpStatus.SERVICE_UNAVAILABLE]: "Serviço temporariamente indisponível. Tente novamente em alguns minutos.",
		};

		// Se já temos uma mensagem específica e amigável, usar ela
		if (originalMessage && this.isUserFriendlyMessage(originalMessage)) {
			// Se for um array, juntar as mensagens
			if (Array.isArray(originalMessage)) {
				return originalMessage.join(". ");
			}
			return originalMessage;
		}

		// Caso contrário, usar mensagem padrão amigável
		return friendlyMessages[status] || "Ocorreu um erro inesperado. Tente novamente mais tarde.";
	}

	private isUserFriendlyMessage(message: string | string[]): boolean {
		// Verificar se a mensagem já é amigável (não contém termos técnicos)
		const technicalTerms = [
			"validation failed",
			"constraint",
			"foreign key",
			"duplicate key",
			"null value",
			"syntax error",
			"connection",
			"timeout",
			"stack trace",
			"exception",
			"Error:",
			"TypeError",
			"ReferenceError",
		];

		// Se for um array, verificar todas as mensagens
		if (Array.isArray(message)) {
			return message.every(msg => {
				if (typeof msg !== "string") return false;
				const lowerMessage = msg.toLowerCase();
				return !technicalTerms.some(term => lowerMessage.includes(term.toLowerCase()));
			});
		}

		// Se for uma string
		if (typeof message !== "string") return false;
		const lowerMessage = message.toLowerCase();
		return !technicalTerms.some(term => lowerMessage.includes(term.toLowerCase()));
	}
	private getErrorType(status: HttpStatus): string {
		// Mapear códigos de status para tipos de erro mais descritivos
		const errorTypes: Record<number, string> = {
			[HttpStatus.BAD_REQUEST]: "INVALID_INPUT",
			[HttpStatus.UNAUTHORIZED]: "AUTHENTICATION_REQUIRED",
			[HttpStatus.FORBIDDEN]: "ACCESS_DENIED",
			[HttpStatus.NOT_FOUND]: "RESOURCE_NOT_FOUND",
			[HttpStatus.CONFLICT]: "RESOURCE_CONFLICT",
			[HttpStatus.UNPROCESSABLE_ENTITY]: "VALIDATION_ERROR",
			[HttpStatus.TOO_MANY_REQUESTS]: "RATE_LIMIT_EXCEEDED",
			[HttpStatus.INTERNAL_SERVER_ERROR]: "INTERNAL_SERVER_ERROR",
			[HttpStatus.BAD_GATEWAY]: "SERVICE_UNAVAILABLE",
			[HttpStatus.SERVICE_UNAVAILABLE]: "SERVICE_UNAVAILABLE",
		};

		return errorTypes[status] || "UNKNOWN_ERROR";
	}
}
