import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from "typeorm";
import { User } from "../../../user/models/entities/user.entity";
import { ImageStatus, ImageType } from "../enums";

/**
 * Entidade Image seguindo princípios SOLID
 * Single Responsibility: Representa apenas dados de imagem
 * Open/Closed: Extensível através de herança se necessário
 */
@Entity("images")
export class Image {
	@PrimaryGeneratedColumn()
	id: number;

	@Column({ type: "varchar", length: 255, unique: true })
	filename: string;

	@Column({ type: "varchar", length: 255 })
	originalName: string;

	@Column({ type: "varchar", length: 500 })
	path: string;

	@Column({ type: "varchar", length: 500, nullable: true })
	url?: string;

	@Column({ type: "enum", enum: ImageType })
	mimeType: ImageType;

	@Column({ type: "bigint" })
	size: number;

	@Column({ type: "int", nullable: true })
	width?: number;

	@Column({ type: "int", nullable: true })
	height?: number;

	@Column({ type: "enum", enum: ImageStatus, default: ImageStatus.UPLOADING })
	status: ImageStatus;

	@Column({ type: "varchar", length: 32, nullable: true })
	hash?: string;

	@Column({ type: "text", nullable: true })
	metadata?: string;

	@Column({ type: "int", nullable: true })
	uploadedBy?: number;

	@ManyToOne(() => User, { nullable: true, onDelete: "SET NULL" })
	@JoinColumn({ name: "uploadedBy" })
	uploader?: User;

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;

	@Column({ type: "timestamp", nullable: true })
	deletedAt?: Date;

	/**
	 * Método para verificar se a imagem está disponível
	 */
	isAvailable(): boolean {
		return this.status === ImageStatus.ACTIVE && !this.deletedAt;
	}

	/**
	 * Método para verificar se a imagem está em processamento
	 */
	isProcessing(): boolean {
		return this.status === ImageStatus.UPLOADING || this.status === ImageStatus.PROCESSING;
	}

	/**
	 * Método para obter URL completa da imagem
	 */
	getFullUrl(baseUrl?: string): string {
		if (this.url) {
			return this.url;
		}
		return baseUrl ? `${baseUrl}/${this.path}` : this.path;
	}

	/**
	 * Método para obter informações de tamanho formatadas
	 */
	getFormattedSize(): string {
		const bytes = Number(this.size);
		if (bytes === 0) return "0 Bytes";

		const k = 1024;
		const sizes = ["Bytes", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));

		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
	}

	/**
	 * Método para obter dimensões formatadas
	 */
	getDimensions(): string | null {
		if (this.width && this.height) {
			return `${this.width}x${this.height}`;
		}
		return null;
	}
}
