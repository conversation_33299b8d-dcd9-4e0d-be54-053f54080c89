import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { ReviewReaction } from "../models/entities/review-reaction.entity";
import { IReviewReaction, IReviewReactionRepository } from "../models/interfaces";

@Injectable()
export class ReviewReactionRepository implements IReviewReactionRepository {
	constructor(
		@InjectRepository(ReviewReaction)
		private readonly reactionRepository: Repository<ReviewReaction>
	) {}

	async create(reaction: Partial<IReviewReaction>): Promise<IReviewReaction> {
		const newReaction = this.reactionRepository.create(reaction);
		return await this.reactionRepository.save(newReaction);
	}

	async delete(id: string): Promise<void> {
		await this.reactionRepository.delete(id);
	}

	async findById(id: string): Promise<IReviewReaction> {
		return await this.reactionRepository.findOne({
			where: { id },
		});
	}

	async findByReviewAndUser(reviewId: string, userId: string): Promise<IReviewReaction> {
		return await this.reactionRepository.findOne({
			where: { reviewId, userId: Number(userId) },
		});
	}

	async findAllByReviewId(reviewId: string): Promise<IReviewReaction[]> {
		return await this.reactionRepository.find({
			where: { reviewId },
		});
	}
}
