import { ApiPropertyOptional } from '@nestjs/swagger';
import {
	IsEnum,
	IsOptional,
	IsString,
	IsNumber,
	IsInt,
	Min,
	Max,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ReadingStatus } from '../enums';
import { WorkType } from '../../../works/models/enums';

export class ReadingFiltersDto {
	@ApiPropertyOptional({
		description: 'Status de leitura',
		enum: ReadingStatus,
		example: ReadingStatus.READING,
	})
	@IsOptional()
	@IsEnum(ReadingStatus)
	status?: ReadingStatus;

	@ApiPropertyOptional({
		description: 'Tipo da obra',
		enum: WorkType,
		example: WorkType.MANHWA,
	})
	@IsOptional()
	@IsEnum(WorkType)
	workType?: WorkType;

	@ApiPropertyOptional({
		description: 'Avaliação mínima',
		example: 4.0,
		minimum: 0,
		maximum: 5,
	})
	@IsOptional()
	@IsNumber()
	@Min(0)
	@Max(5)
	@Type(() => Number)
	minRating?: number;

	@ApiPropertyOptional({
		description: 'Avaliação máxima',
		example: 5.0,
		minimum: 0,
		maximum: 5,
	})
	@IsOptional()
	@IsNumber()
	@Min(0)
	@Max(5)
	@Type(() => Number)
	maxRating?: number;

	@ApiPropertyOptional({
		description: 'Busca por título da obra',
		example: 'Solo Leveling',
	})
	@IsOptional()
	@IsString()
	search?: string;

	@ApiPropertyOptional({
		description: 'Página',
		example: 1,
		minimum: 1,
		default: 1,
	})
	@IsOptional()
	@IsInt()
	@Min(1)
	@Type(() => Number)
	page?: number = 1;

	@ApiPropertyOptional({
		description: 'Itens por página',
		example: 20,
		minimum: 1,
		maximum: 100,
		default: 20,
	})
	@IsOptional()
	@IsInt()
	@Min(1)
	@Max(100)
	@Type(() => Number)
	limit?: number = 20;

	@ApiPropertyOptional({
		description: 'Campo para ordenação',
		enum: ['lastReadAt', 'startedAt', 'completedAt', 'personalRating', 'createdAt'],
		example: 'lastReadAt',
		default: 'lastReadAt',
	})
	@IsOptional()
	@IsEnum(['lastReadAt', 'startedAt', 'completedAt', 'personalRating', 'createdAt'])
	sortBy?: 'lastReadAt' | 'startedAt' | 'completedAt' | 'personalRating' | 'createdAt' = 'lastReadAt';

	@ApiPropertyOptional({
		description: 'Ordem da ordenação',
		enum: ['ASC', 'DESC'],
		example: 'DESC',
		default: 'DESC',
	})
	@IsOptional()
	@IsEnum(['ASC', 'DESC'])
	sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
