import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "./modules/auth/auth.module";
import { UserModule } from "./modules/user/user.module";
import { WorksModule } from "./modules/works/works.module";
import { ReadingModule } from "./modules/reading/reading.module";
import { ListsModule } from "./modules/lists/lists.module";
import { RankingsModule } from "./modules/rankings/rankings.module";
import { PermissionsModule } from "./modules/permissions/permissions.module";
import { DatabaseConfigService } from "./shared/databases/database.config.service";
import { TagsModule } from "./modules/tags/tags.module";
import { ReviewsModule } from "./modules/reviews/reviews.module";
import { ImagesModule } from "./modules/images/images.module";

@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
		}),
		TypeOrmModule.forRootAsync({
			useClass: DatabaseConfigService,
		}),
		AuthModule,
		UserModule,
		PermissionsModule,
		WorksModule,
		ReadingModule,
		ListsModule,
		RankingsModule,
		TagsModule,
		ReviewsModule,
		ImagesModule,
	],
	controllers: [],
	providers: [],
})
export class AppModule {}
