import { Injectable, Inject } from "@nestjs/common";
import { ResourceNotFoundException, ValidationException, UnauthorizedOperationException } from "src/shared/exceptions/business.exceptions";
import { IAssociateCoverImageRequest, IWorkRepository } from "../../models/interfaces";
import { IImageRepository } from "../../../images/models/interfaces";
import { UserRole } from "../../../user/models/enums";
import { ImageAssociationResponseDto } from "../../models/dtos";

/**
 * Use Case para associar imagem de capa a uma obra
 * Seguindo princípios SOLID - Single Responsibility
 */
@Injectable()
export class AssociateCoverImageUseCase {
	constructor(
		@Inject("IWorkRepository")
		private readonly workRepository: IWorkRepository,
		@Inject("IImageRepository")
		private readonly imageRepository: IImageRepository
	) {}

	async execute(data: IAssociateCoverImageRequest, userRole?: UserRole): Promise<ImageAssociationResponseDto> {
		const { workId, imageId, userId } = data;

		// 1. Verificar se a obra existe
		const work = await this.workRepository.findById(workId);
		if (!work) {
			throw new ResourceNotFoundException("Obra", workId);
		}

		// 2. Verificar se a imagem existe e está ativa
		const image = await this.imageRepository.findById(imageId);
		if (!image) {
			throw new ResourceNotFoundException("Imagem", imageId.toString());
		}

		if (!image.isAvailable()) {
			throw new ValidationException({
				imageId: ["A imagem não está disponível para uso"],
			});
		}

		// 3. Validar propriedade da imagem (apenas proprietário ou admin pode associar)
		const isAdmin = userRole === UserRole.ADMIN;
		const isOwner = image.uploadedBy === userId;

		if (!isAdmin && !isOwner) {
			throw new UnauthorizedOperationException("Você não tem permissão para usar esta imagem");
		}

		// 4. Associar a imagem como capa
		await this.workRepository.associateCoverImage(workId, imageId);

		return {
			success: true,
			message: "Imagem de capa associada com sucesso",
			workId,
			imageIds: [imageId],
		};
	}
}
