/**
 * Enum para status de imagem no sistema
 * Seguindo princípios SOLID - Single Responsibility
 */
export enum ImageStatus {
	UPLOADING = "uploading",
	PROCESSING = "processing",
	ACTIVE = "active",
	DELETED = "deleted",
	FAILED = "failed",
}

/**
 * Status que indicam que a imagem está disponível para uso
 */
export const AVAILABLE_STATUSES = [ImageStatus.ACTIVE];

/**
 * Status que indicam que a imagem está em processamento
 */
export const PROCESSING_STATUSES = [ImageStatus.UPLOADING, ImageStatus.PROCESSING];

/**
 * Status que indicam que a imagem não está disponível
 */
export const UNAVAILABLE_STATUSES = [ImageStatus.DELETED, ImageStatus.FAILED];
