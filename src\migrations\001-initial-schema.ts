import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialSchema1734346150000 implements MigrationInterface {
	name = "InitialSchema1734346150000";

	public async up(queryRunner: QueryRunner): Promise<void> {
		// Verificar se as tabelas já existem antes de tentar criar

		// Criar enum types
		await queryRunner.query(`
			DO $$ BEGIN
				CREATE TYPE "work_type_enum" AS ENUM('manhwa', 'manhua', 'manga');
			EXCEPTION
				WHEN duplicate_object THEN null;
			END $$;
		`);

		await queryRunner.query(`
			DO $$ BEGIN
				CREATE TYPE "work_status_enum" AS ENUM('ongoing', 'completed', 'hiatus', 'cancelled');
			EXCEPTION
				WHEN duplicate_object THEN null;
			END $$;
		`);

		await queryRunner.query(`
			DO $$ BEGIN
				CREATE TYPE "reading_status_enum" AS ENUM('reading', 'completed', 'dropped', 'plan_to_read', 'on_hold');
			EXCEPTION
				WHEN duplicate_object THEN null;
			END $$;
		`);

		// Criar tabela works
		await queryRunner.query(`
			CREATE TABLE IF NOT EXISTS "works" (
				"id" uuid NOT NULL DEFAULT uuid_generate_v4(),
				"title" character varying(255) NOT NULL,
				"originalTitle" character varying(255),
				"description" text,
				"coverImage" character varying(500),
				"type" "work_type_enum" NOT NULL,
				"status" "work_status_enum" NOT NULL,
				"author" character varying(255),
				"artist" character varying(255),
				"totalChapters" integer,
				"releaseDate" date,
				"averageRating" numeric(3,2) NOT NULL DEFAULT '0',
				"totalReviews" integer NOT NULL DEFAULT '0',
				"createdAt" TIMESTAMP NOT NULL DEFAULT now(),
				"updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
				CONSTRAINT "PK_works" PRIMARY KEY ("id")
			)
		`);

		// Criar tabela chapters
		await queryRunner.query(`
			CREATE TABLE IF NOT EXISTS "chapters" (
				"id" uuid NOT NULL DEFAULT uuid_generate_v4(),
				"workId" uuid NOT NULL,
				"number" integer NOT NULL,
				"title" character varying(255),
				"releaseDate" date,
				"url" character varying(500),
				"createdAt" TIMESTAMP NOT NULL DEFAULT now(),
				CONSTRAINT "PK_chapters" PRIMARY KEY ("id"),
				CONSTRAINT "UQ_chapters_work_number" UNIQUE ("workId", "number")
			)
		`);

		// Criar tabela user_works
		await queryRunner.query(`
			CREATE TABLE IF NOT EXISTS "user_works" (
				"id" uuid NOT NULL DEFAULT uuid_generate_v4(),
				"userId" uuid NOT NULL,
				"workId" uuid NOT NULL,
				"status" "reading_status_enum" NOT NULL DEFAULT 'plan_to_read',
				"currentChapter" integer NOT NULL DEFAULT '0',
				"personalRating" numeric(3,2),
				"startedAt" TIMESTAMP,
				"completedAt" TIMESTAMP,
				"lastReadAt" TIMESTAMP,
				"createdAt" TIMESTAMP NOT NULL DEFAULT now(),
				"updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
				CONSTRAINT "PK_user_works" PRIMARY KEY ("id"),
				CONSTRAINT "UQ_user_works_user_work" UNIQUE ("userId", "workId")
			)
		`);

		// Adicionar foreign keys
		await queryRunner.query(`
			DO $$ BEGIN
				ALTER TABLE "chapters"
				ADD CONSTRAINT "FK_chapters_work"
				FOREIGN KEY ("workId") REFERENCES "works"("id") ON DELETE CASCADE;
			EXCEPTION
				WHEN duplicate_object THEN null;
			END $$;
		`);

		await queryRunner.query(`
			DO $$ BEGIN
				ALTER TABLE "user_works"
				ADD CONSTRAINT "FK_user_works_work"
				FOREIGN KEY ("workId") REFERENCES "works"("id") ON DELETE CASCADE;
			EXCEPTION
				WHEN duplicate_object THEN null;
			END $$;
		`);

		// Criar índices para performance
		await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_chapters_workId" ON "chapters" ("workId")`);
		await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_user_works_userId" ON "user_works" ("userId")`);
		await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_user_works_workId" ON "user_works" ("workId")`);
		await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_user_works_status" ON "user_works" ("status")`);
		await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_works_type" ON "works" ("type")`);
		await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_works_status" ON "works" ("status")`);
		await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_works_title" ON "works" ("title")`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Dropar tabelas
		await queryRunner.query(`DROP TABLE IF EXISTS "user_works" CASCADE`);
		await queryRunner.query(`DROP TABLE IF EXISTS "chapters" CASCADE`);
		await queryRunner.query(`DROP TABLE IF EXISTS "works" CASCADE`);

		// Dropar enums
		await queryRunner.query(`DROP TYPE IF EXISTS "reading_status_enum"`);
		await queryRunner.query(`DROP TYPE IF EXISTS "work_status_enum"`);
		await queryRunner.query(`DROP TYPE IF EXISTS "work_type_enum"`);
	}
}
