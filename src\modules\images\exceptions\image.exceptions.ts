import { HttpException, HttpStatus } from '@nestjs/common';

/**
 * Exceção base para operações de imagem
 */
export abstract class ImageException extends HttpException {
	constructor(message: string, status: HttpStatus) {
		super(message, status);
	}
}

/**
 * Exceção para validação de imagem
 */
export class ImageValidationException extends ImageException {
	constructor(message: string, details?: any) {
		super(message, HttpStatus.BAD_REQUEST);
		this.name = 'ImageValidationException';
		
		if (details) {
			this.message = `${message}. Detalhes: ${JSON.stringify(details)}`;
		}
	}
}

/**
 * Exceção para processamento de imagem
 */
export class ImageProcessingException extends ImageException {
	constructor(message: string, originalError?: Error) {
		super(message, HttpStatus.UNPROCESSABLE_ENTITY);
		this.name = 'ImageProcessingException';
		
		if (originalError) {
			this.message = `${message}. Erro original: ${originalError.message}`;
		}
	}
}

/**
 * Exceção para upload de imagem
 */
export class ImageUploadException extends ImageException {
	constructor(message: string, filename?: string) {
		super(message, HttpStatus.BAD_REQUEST);
		this.name = 'ImageUploadException';
		
		if (filename) {
			this.message = `${message} (Arquivo: ${filename})`;
		}
	}
}

/**
 * Exceção para imagem não encontrada
 */
export class ImageNotFoundException extends ImageException {
	constructor(identifier: string | number) {
		super(`Imagem não encontrada: ${identifier}`, HttpStatus.NOT_FOUND);
		this.name = 'ImageNotFoundException';
	}
}

/**
 * Exceção para arquivo de imagem não encontrado
 */
export class ImageFileNotFoundException extends ImageException {
	constructor(filePath: string) {
		super(`Arquivo de imagem não encontrado: ${filePath}`, HttpStatus.NOT_FOUND);
		this.name = 'ImageFileNotFoundException';
	}
}

/**
 * Exceção para permissão negada
 */
export class ImagePermissionException extends ImageException {
	constructor(action: string, imageId?: number) {
		const message = imageId 
			? `Permissão negada para ${action} na imagem ${imageId}`
			: `Permissão negada para ${action}`;
		
		super(message, HttpStatus.FORBIDDEN);
		this.name = 'ImagePermissionException';
	}
}

/**
 * Exceção para limite de armazenamento
 */
export class ImageStorageLimitException extends ImageException {
	constructor(currentSize: number, maxSize: number) {
		const currentSizeMB = (currentSize / (1024 * 1024)).toFixed(2);
		const maxSizeMB = (maxSize / (1024 * 1024)).toFixed(2);
		
		super(
			`Limite de armazenamento excedido. Atual: ${currentSizeMB}MB, Máximo: ${maxSizeMB}MB`,
			HttpStatus.PAYLOAD_TOO_LARGE
		);
		this.name = 'ImageStorageLimitException';
	}
}

/**
 * Exceção para imagem duplicada
 */
export class ImageDuplicateException extends ImageException {
	constructor(hash: string, existingImageId: number) {
		super(
			`Imagem duplicada detectada. Hash: ${hash}, Imagem existente: ${existingImageId}`,
			HttpStatus.CONFLICT
		);
		this.name = 'ImageDuplicateException';
	}
}

/**
 * Exceção para formato não suportado
 */
export class ImageFormatException extends ImageException {
	constructor(format: string, supportedFormats: string[]) {
		super(
			`Formato de imagem não suportado: ${format}. Formatos suportados: ${supportedFormats.join(', ')}`,
			HttpStatus.UNSUPPORTED_MEDIA_TYPE
		);
		this.name = 'ImageFormatException';
	}
}

/**
 * Exceção para dimensões inválidas
 */
export class ImageDimensionException extends ImageException {
	constructor(width: number, height: number, maxWidth: number, maxHeight: number) {
		super(
			`Dimensões da imagem inválidas: ${width}x${height}. Máximo permitido: ${maxWidth}x${maxHeight}`,
			HttpStatus.BAD_REQUEST
		);
		this.name = 'ImageDimensionException';
	}
}

/**
 * Exceção para operação de limpeza
 */
export class ImageCleanupException extends ImageException {
	constructor(message: string, affectedFiles?: string[]) {
		super(message, HttpStatus.INTERNAL_SERVER_ERROR);
		this.name = 'ImageCleanupException';
		
		if (affectedFiles && affectedFiles.length > 0) {
			this.message = `${message}. Arquivos afetados: ${affectedFiles.join(', ')}`;
		}
	}
}

/**
 * Exceção para operação de sistema de arquivos
 */
export class ImageFileSystemException extends ImageException {
	constructor(operation: string, filePath: string, originalError?: Error) {
		super(
			`Erro na operação de sistema de arquivos: ${operation} em ${filePath}`,
			HttpStatus.INTERNAL_SERVER_ERROR
		);
		this.name = 'ImageFileSystemException';
		
		if (originalError) {
			this.message = `${this.message}. Erro: ${originalError.message}`;
		}
	}
}

/**
 * Exceção para configuração inválida
 */
export class ImageConfigurationException extends ImageException {
	constructor(configKey: string, expectedValue?: string) {
		const message = expectedValue 
			? `Configuração inválida para ${configKey}. Esperado: ${expectedValue}`
			: `Configuração inválida para ${configKey}`;
		
		super(message, HttpStatus.INTERNAL_SERVER_ERROR);
		this.name = 'ImageConfigurationException';
	}
}
