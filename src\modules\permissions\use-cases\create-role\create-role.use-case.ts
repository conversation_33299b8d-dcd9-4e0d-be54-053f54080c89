import { Injectable, Inject, ConflictException } from "@nestjs/common";
import { BaseUseCase } from "../../../../shared/use-cases/base.use-case";
import { IRoleRepository, IPermissionRepository } from "../../models/interfaces";
import { CreateRoleDto } from "../../models/dtos";
import { RoleWithPermissionsResponseDto } from "../../models/dtos";
import { Role } from "../../models/entities";

/**
 * Use case para criar um novo role no sistema
 */
@Injectable()
export class CreateRoleUseCase extends BaseUseCase<CreateRoleDto, RoleWithPermissionsResponseDto> {
	constructor(
		@Inject("IRoleRepository")
		private readonly roleRepository: IRoleRepository,
		@Inject("IPermissionRepository")
		private readonly permissionRepository: IPermissionRepository
	) {
		super("CreateRoleUseCase");
	}

	/**
	 * Executa a criação de um novo role
	 *
	 * @param createRoleDto - Dados para criação do role
	 * @returns Role criado com suas permissões
	 */
	async execute(createRoleDto: CreateRoleDto): Promise<RoleWithPermissionsResponseDto> {
		this.logger.log(`Iniciando criação de role: ${createRoleDto.name}`);

		try {
			// Verifica se já existe um role com o mesmo nome
			const existingRole = await this.roleRepository.findByName(createRoleDto.name);
			if (existingRole) {
				throw new ConflictException(`Role com nome '${createRoleDto.name}' já existe`);
			}

			// Cria o role básico
			const roleData: Partial<Role> = {
				name: createRoleDto.name,
				description: createRoleDto.description,
				isActive: createRoleDto.isActive ?? true,
				isSystem: false, // Roles criados via API não são do sistema
			};

			const createdRole = await this.roleRepository.create(roleData);
			this.logger.log(`Role criado com ID: ${createdRole.id}`);

			// Se foram fornecidas permissões, adiciona elas ao role
			if (createRoleDto.permissionIds && createRoleDto.permissionIds.length > 0) {
				// Verifica se todas as permissões existem
				const permissions = await this.permissionRepository.findByIds(createRoleDto.permissionIds);

				if (permissions.length !== createRoleDto.permissionIds.length) {
					const foundIds = permissions.map(p => p.id);
					const missingIds = createRoleDto.permissionIds.filter(id => !foundIds.includes(id));
					this.logger.warn(`Permissões não encontradas: ${missingIds.join(", ")}`);
				}

				if (permissions.length > 0) {
					await this.roleRepository.addPermissions(
						createdRole.id,
						permissions.map(p => p.id)
					);
					this.logger.log(`${permissions.length} permissões adicionadas ao role`);
				}
			}

			// Busca o role criado com suas permissões
			const roleWithPermissions = await this.roleRepository.findByIdWithPermissions(createdRole.id);

			this.logger.log(`Role '${createRoleDto.name}' criado com sucesso`);

			return this.mapToResponseDto(roleWithPermissions);
		} catch (error) {
			this.logger.error(`Erro ao criar role: ${error.message}`, error.stack);
			throw error;
		}
	}

	/**
	 * Mapeia a entidade Role para o DTO de resposta
	 */
	private mapToResponseDto(role: Role): RoleWithPermissionsResponseDto {
		return {
			id: role.id,
			name: role.name,
			description: role.description,
			isActive: role.isActive,
			isSystem: role.isSystem,
			createdAt: role.createdAt,
			updatedAt: role.updatedAt,
			permissions: role.permissions?.map(p => p.name) || [],
		};
	}
}
