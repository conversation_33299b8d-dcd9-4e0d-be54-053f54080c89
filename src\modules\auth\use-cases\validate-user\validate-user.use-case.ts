import { Inject, Injectable } from "@nestjs/common";
import * as bcrypt from "bcryptjs";
import { DatabaseException } from "../../../../shared/exceptions/business.exceptions";
import { IAuthRepository } from "../../models/interfaces/auth-repository.interface";

@Injectable()
export class ValidateUserUseCase {
	constructor(
		@Inject("IAuthRepository")
		private readonly authRepository: IAuthRepository
	) {}

	async execute(usernameOrEmail: string, password: string): Promise<any> {
		try {
			const user = await this.authRepository.findByUsernameOrEmail(usernameOrEmail);
			if (user && (await bcrypt.compare(password, user.password))) {
				const { password, ...result } = user;
				return result;
			}
			return null;
		} catch (error) {
			throw new DatabaseException("validação de usuário", error);
		}
	}
}
