import { Inject, Injectable } from "@nestjs/common";
import { DuplicateResourceException, ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { WorkRepository } from "../../../works/repositories";
import { ICreateUserWorkRequest, IUserWork, IUserWorkRepository } from "../../models/interfaces";

@Injectable()
export class AddToReadingUseCase {
	constructor(
		@Inject("IUserWorkRepository")
		private readonly userWorkRepository: IUserWorkRepository,
		private readonly workRepository: WorkRepository
	) {}

	async execute(userId: string, data: ICreateUserWorkRequest): Promise<IUserWork> {
		// Verificar se a obra existe
		const work = await this.workRepository.findById(data.workId);
		if (!work) {
			throw new ResourceNotFoundException("Obra", data.workId);
		}

		// Verificar se o usuário já não tem esta obra em sua lista
		const existingUserWork = await this.userWorkRepository.findByUserAndWork(Number(userId), data.workId);
		if (existingUserWork) {
			throw new DuplicateResourceException("Obra na lista de leitura", "workId", data.workId);
		}

		const createData: ICreateUserWorkRequest = {
			...data,
			userId: Number(userId),
		};

		return await this.userWorkRepository.create(createData);
	}
}
