import { ReadingStatus } from "../enums";

export interface IUserWork {
	id: string;
	userId: number;
	workId: string;
	status: ReadingStatus;
	currentChapter: number;
	personalRating?: number;
	startedAt?: Date;
	completedAt?: Date;
	lastReadAt?: Date;
	createdAt: Date;
	updatedAt: Date;
}

export interface ICreateUserWorkRequest {
	userId: number;
	workId: string;
	status: ReadingStatus;
	currentChapter?: number;
	personalRating?: number;
}

export interface IUpdateUserWorkRequest {
	status?: ReadingStatus;
	currentChapter?: number;
	personalRating?: number;
}

export interface IUserWorkFilters {
	userId: number;
	status?: ReadingStatus;
	workType?: string;
	minRating?: number;
	maxRating?: number;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: "lastReadAt" | "startedAt" | "completedAt" | "personalRating" | "createdAt";
	sortOrder?: "ASC" | "DESC";
}

export interface IUserWorkRepository {
	create(data: ICreateUserWorkRequest): Promise<IUserWork>;
	findById(id: string): Promise<IUserWork | null>;
	findByUserAndWork(userId: number, workId: string): Promise<IUserWork | null>;
	findByUser(filters: IUserWorkFilters): Promise<{ userWorks: IUserWork[]; total: number }>;
	update(id: string, data: IUpdateUserWorkRequest): Promise<IUserWork>;
	delete(id: string): Promise<void>;
	updateProgress(userId: number, workId: string, currentChapter: number): Promise<IUserWork>;
	getReadingStats(userId: number): Promise<{
		reading: number;
		completed: number;
		dropped: number;
		planToRead: number;
		onHold: number;
		totalWorks: number;
	}>;
	getTotalChaptersRead(userId: number): Promise<number>;
	getAverageReadingTime(userId: number): Promise<number | null>;
	getLastReadingActivity(userId: number): Promise<Date | null>;
	getFavoriteGenre(userId: number): Promise<string | null>;
}
