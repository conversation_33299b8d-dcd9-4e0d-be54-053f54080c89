import { ImageType } from "../enums";

/**
 * Interface para validação de arquivos de imagem
 * Seguindo Interface Segregation Principle (ISP)
 */
export interface IImageValidationService {
	/**
	 * Valida se o arquivo é uma imagem válida
	 */
	validateImageFile(file: Express.Multer.File): Promise<boolean>;

	/**
	 * Valida tipo MIME da imagem
	 */
	validateMimeType(mimeType: string): boolean;

	/**
	 * Valida tamanho do arquivo
	 */
	validateFileSize(size: number, maxSizeInMB?: number): boolean;

	/**
	 * Valida dimensões da imagem
	 */
	validateDimensions(width: number, height: number, maxWidth?: number, maxHeight?: number): boolean;
}

/**
 * Interface para processamento de imagens
 * Seguindo Single Responsibility Principle (SRP)
 */
export interface IImageProcessingService {
	/**
	 * Obtém metadados da imagem
	 */
	getImageMetadata(filePath: string): Promise<{
		width: number;
		height: number;
		format: string;
		size: number;
	}>;

	/**
	 * Gera hash da imagem para detectar duplicatas
	 */
	generateImageHash(filePath: string): Promise<string>;

	/**
	 * Redimensiona imagem se necessário
	 */
	resizeImage(inputPath: string, outputPath: string, maxWidth: number, maxHeight: number): Promise<void>;

	/**
	 * Otimiza imagem para web
	 */
	optimizeImage(inputPath: string, outputPath: string, quality?: number): Promise<void>;
}

/**
 * Interface para gerenciamento de arquivos
 * Seguindo Single Responsibility Principle (SRP)
 */
export interface IImageFileService {
	/**
	 * Salva arquivo no sistema de arquivos
	 */
	saveFile(file: Express.Multer.File, destinationPath: string): Promise<string>;

	/**
	 * Move arquivo de temp para permanente
	 */
	moveFromTemp(tempPath: string, permanentPath: string): Promise<void>;

	/**
	 * Remove arquivo do sistema
	 */
	deleteFile(filePath: string): Promise<void>;

	/**
	 * Verifica se arquivo existe
	 */
	fileExists(filePath: string): Promise<boolean>;

	/**
	 * Gera nome único para arquivo
	 */
	generateUniqueFilename(originalName: string, mimeType: ImageType): string;

	/**
	 * Sanitiza nome do arquivo
	 */
	sanitizeFilename(filename: string): string;

	/**
	 * Obtém caminho completo do arquivo
	 */
	getFullPath(relativePath: string): string;

	/**
	 * Lista arquivos de imagem no diretório
	 */
	listFiles(directory: string, extensions?: string[]): Promise<string[]>;
}

/**
 * Interface para limpeza automática
 * Seguindo Single Responsibility Principle (SRP)
 */
export interface IImageCleanupService {
	/**
	 * Limpa arquivos temporários antigos
	 */
	cleanupTempFiles(olderThanMinutes: number): Promise<number>;

	/**
	 * Limpa imagens órfãs (sem referência no banco)
	 */
	cleanupOrphanedFiles(): Promise<number>;

	/**
	 * Limpa imagens marcadas como deletadas
	 */
	cleanupDeletedImages(): Promise<number>;

	/**
	 * Executa limpeza completa
	 */
	performFullCleanup(): Promise<{
		tempFilesRemoved: number;
		orphanedFilesRemoved: number;
		deletedImagesRemoved: number;
	}>;
}
