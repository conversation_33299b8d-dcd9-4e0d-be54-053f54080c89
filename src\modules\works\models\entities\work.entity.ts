import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToMany, ManyToOne, JoinTable, Join<PERSON><PERSON>um<PERSON> } from "typeorm";
import { WorkType, WorkStatus } from "../enums";
import { Chapter } from "./chapter.entity";
import { Image } from "../../../images/models/entities/image.entity";

@Entity("works")
export class Work {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "varchar", length: 255 })
	title: string;

	@Column({ type: "varchar", length: 255, nullable: true })
	originalTitle?: string;

	@Column({ type: "text", nullable: true })
	description?: string;

	@Column({ type: "int", nullable: true })
	coverImageId?: number;

	@ManyToOne(() => Image, { nullable: true, onDelete: "SET NULL" })
	@JoinColumn({ name: "coverImageId" })
	coverImageEntity?: Image;

	@Column({
		type: "enum",
		enum: WorkType,
	})
	type: WorkType;

	@Column({
		type: "enum",
		enum: WorkStatus,
	})
	status: WorkStatus;

	@Column({ type: "varchar", length: 255, nullable: true })
	author?: string;

	@Column({ type: "varchar", length: 255, nullable: true })
	artist?: string;

	@Column({ type: "integer", nullable: true })
	totalChapters?: number;

	@Column({ type: "date", nullable: true })
	releaseDate?: Date;

	@Column({ type: "decimal", precision: 3, scale: 2, default: 0 })
	averageRating: number;

	@Column({ type: "integer", default: 0 })
	totalReviews: number;

	@OneToMany(() => Chapter, chapter => chapter.work, {
		cascade: true,
		onDelete: "CASCADE",
	})
	chapters: Chapter[];

	@ManyToMany(() => Image, { nullable: true })
	@JoinTable({
		name: "work_images",
		joinColumn: { name: "workId", referencedColumnName: "id" },
		inverseJoinColumn: { name: "imageId", referencedColumnName: "id" },
	})
	galleryImages?: Image[];

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;
}
