import { Inject, Injectable } from "@nestjs/common";
import { ICurrentUserPayload } from "../../../../shared/decorators/current-user.decorator";
import { ResourceNotFoundException } from "../../../../shared/exceptions/business.exceptions";
import { UserProfileResponseDto } from "../../models/dtos/user-profile-response.dto";
import { User } from "../../../user/models/entities/user.entity";
import { IAuthRepository } from "../../models/interfaces/auth-repository.interface";
import { IUserProfileStats } from "../../models/interfaces/user-profile-stats.interface";
import { GetUserProfileStatsUseCase } from "../get-user-profile-stats/get-user-profile-stats.use-case";

@Injectable()
export class GetProfileUseCase {
	constructor(
		@Inject("IAuthRepository")
		private readonly authRepository: IAuthRepository,
		private readonly getUserProfileStatsUseCase: GetUserProfileStatsUseCase
	) {}

	async execute(currentUser: ICurrentUserPayload): Promise<UserProfileResponseDto> {
		// Buscar o usuário atual com dados atualizados
		const user = await this.authRepository.findByUsernameOrEmail(currentUser.username);
		if (!user) {
			throw new ResourceNotFoundException("Usuário", currentUser.username);
		}

		// Buscar estatísticas do usuário
		const stats = await this.getUserProfileStatsUseCase.execute(currentUser.id);

		return this.mapToUserProfileResponseDto(user, stats);
	}

	private mapToUserProfileResponseDto(user: User, stats: IUserProfileStats): UserProfileResponseDto {
		const { password, refreshToken, ...userWithoutSensitiveData } = user;
		return {
			...userWithoutSensitiveData,
			stats,
		} as UserProfileResponseDto;
	}
}
