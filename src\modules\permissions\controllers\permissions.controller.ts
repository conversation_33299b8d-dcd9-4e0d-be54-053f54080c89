import { Controller, Get, Post, Body, Param, UseGuards, Query, ParseIntPipe } from "@nestjs/common";
import {
	ApiTags,
	ApiOperation,
	ApiResponse,
	ApiBearerAuth,
	ApiParam,
	ApiQuery,
	ApiUnauthorizedResponse,
	ApiForbiddenResponse,
	ApiNotFoundResponse,
	ApiConflictResponse,
	ApiInternalServerErrorResponse,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../../auth/models/guard/jwt-auth.guard";
import { RolesGuard } from "../models/guards";
import { Roles } from "../models/decorators";
import { CurrentUser, ICurrentUserPayload } from "../../../shared/decorators/current-user.decorator";
import { UserRole } from "../../user/models/enums";
import { Permission } from "../models/enums";
import {
	CreateRoleDto,
	AssignRoleDto,
	RoleWithPermissionsResponseDto,
	UserRoleWithDetailsResponseDto,
} from "../models/dtos";
import {
	CreateRoleUseCase,
	AssignRoleUseCase,
	ListRolesUseCase,
	GetUserPermissionsUseCase,
} from "../use-cases";
import {
	ErrorResponseDto,
	UnauthorizedErrorResponseDto,
	InternalServerErrorResponseDto,
} from "../../../shared/dtos/error-response.dto";

@ApiTags("permissions")
@Controller("permissions")
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth("JWT-auth")
export class PermissionsController {
	constructor(
		private readonly createRoleUseCase: CreateRoleUseCase,
		private readonly assignRoleUseCase: AssignRoleUseCase,
		private readonly listRolesUseCase: ListRolesUseCase,
		private readonly getUserPermissionsUseCase: GetUserPermissionsUseCase
	) {}

	@Post("roles")
	@Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
	@ApiOperation({
		summary: "🔐 Criar novo role",
		description: `
### 📋 Descrição
Cria um novo role no sistema com permissões específicas.

### 🔒 Permissões Necessárias
- **ADMIN** ou **SUPER_ADMIN**

### ⚡ Funcionalidades
- Criação de roles personalizados
- Atribuição de permissões específicas
- Validação de nomes únicos
		`,
	})
	@ApiResponse({
		status: 201,
		description: "✅ Role criado com sucesso",
		type: RoleWithPermissionsResponseDto,
	})
	@ApiConflictResponse({
		description: "⚠️ Role com este nome já existe",
		type: ErrorResponseDto,
	})
	@ApiForbiddenResponse({
		description: "🚫 Acesso negado - Permissões insuficientes",
		type: ErrorResponseDto,
	})
	@ApiUnauthorizedResponse({
		description: "🔒 Token não fornecido ou inválido",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	createRole(@Body() createRoleDto: CreateRoleDto): Promise<RoleWithPermissionsResponseDto> {
		return this.createRoleUseCase.execute(createRoleDto);
	}

	@Get("roles")
	@Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.MODERATOR)
	@ApiOperation({
		summary: "📋 Listar roles",
		description: `
### 📋 Descrição
Lista todos os roles disponíveis no sistema.

### 🔒 Permissões Necessárias
- **ADMIN**, **SUPER_ADMIN** ou **MODERATOR**
		`,
	})
	@ApiQuery({
		name: "includePermissions",
		required: false,
		type: Boolean,
		description: "Se deve incluir as permissões de cada role",
		example: true,
	})
	@ApiResponse({
		status: 200,
		description: "✅ Lista de roles retornada com sucesso",
		type: [RoleWithPermissionsResponseDto],
	})
	@ApiForbiddenResponse({
		description: "🚫 Acesso negado - Permissões insuficientes",
		type: ErrorResponseDto,
	})
	@ApiUnauthorizedResponse({
		description: "🔒 Token não fornecido ou inválido",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	listRoles(@Query("includePermissions") includePermissions: boolean = true): Promise<RoleWithPermissionsResponseDto[]> {
		return this.listRolesUseCase.execute(includePermissions);
	}

	@Post("assign-role")
	@Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
	@ApiOperation({
		summary: "👤 Atribuir role a usuário",
		description: `
### 📋 Descrição
Atribui um role específico a um usuário.

### 🔒 Permissões Necessárias
- **ADMIN** ou **SUPER_ADMIN**

### ⚡ Funcionalidades
- Atribuição de roles temporários ou permanentes
- Validação de usuário e role existentes
- Prevenção de duplicação de roles
		`,
	})
	@ApiResponse({
		status: 201,
		description: "✅ Role atribuído com sucesso",
		type: UserRoleWithDetailsResponseDto,
	})
	@ApiNotFoundResponse({
		description: "❌ Usuário ou role não encontrado",
		type: ErrorResponseDto,
	})
	@ApiConflictResponse({
		description: "⚠️ Usuário já possui este role",
		type: ErrorResponseDto,
	})
	@ApiForbiddenResponse({
		description: "🚫 Acesso negado - Permissões insuficientes",
		type: ErrorResponseDto,
	})
	@ApiUnauthorizedResponse({
		description: "🔒 Token não fornecido ou inválido",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	assignRole(
		@Body() assignRoleDto: AssignRoleDto,
		@CurrentUser() currentUser: ICurrentUserPayload
	): Promise<UserRoleWithDetailsResponseDto> {
		return this.assignRoleUseCase.execute(assignRoleDto, currentUser.id);
	}

	@Get("users/:userId/permissions")
	@Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN, UserRole.MODERATOR)
	@ApiOperation({
		summary: "🔍 Buscar permissões do usuário",
		description: `
### 📋 Descrição
Retorna todas as permissões de um usuário específico.

### 🔒 Permissões Necessárias
- **ADMIN**, **SUPER_ADMIN** ou **MODERATOR**
		`,
	})
	@ApiParam({
		name: "userId",
		description: "ID do usuário",
		example: 123,
		type: Number,
	})
	@ApiResponse({
		status: 200,
		description: "✅ Permissões do usuário retornadas com sucesso",
		schema: {
			type: "array",
			items: {
				type: "string",
				enum: Object.values(Permission),
			},
			example: [Permission.USERS_READ, Permission.WORKS_CREATE, Permission.REVIEWS_MODERATE],
		},
	})
	@ApiNotFoundResponse({
		description: "❌ Usuário não encontrado",
		type: ErrorResponseDto,
	})
	@ApiForbiddenResponse({
		description: "🚫 Acesso negado - Permissões insuficientes",
		type: ErrorResponseDto,
	})
	@ApiUnauthorizedResponse({
		description: "🔒 Token não fornecido ou inválido",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	getUserPermissions(@Param("userId", ParseIntPipe) userId: number): Promise<Permission[]> {
		return this.getUserPermissionsUseCase.execute(userId);
	}
}
