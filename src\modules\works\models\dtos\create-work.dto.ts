import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsString, IsNotEmpty, IsEnum, IsOptional, IsInt, IsDateString, MaxLength, Min, IsUrl, IsArray, IsNumber } from "class-validator";
import { WorkType, WorkStatus } from "../enums";

export class CreateWorkDto {
	@ApiProperty({
		description: "Título da obra",
		example: "Solo Leveling",
		maxLength: 255,
	})
	@IsString()
	@IsNotEmpty()
	@MaxLength(255)
	title: string;

	@ApiPropertyOptional({
		description: "Título original da obra",
		example: "나 혼자만 레벨업",
		maxLength: 255,
	})
	@IsOptional()
	@IsString()
	@MaxLength(255)
	originalTitle?: string;

	@ApiPropertyOptional({
		description: "Descrição da obra",
		example: "Um caçador fraco se torna o mais forte...",
	})
	@IsOptional()
	@IsString()
	description?: string;

	@ApiPropertyOptional({
		description: "ID da imagem de capa (referência para imagem já uploadada)",
		example: 123,
	})
	@IsOptional()
	@IsNumber()
	coverImageId?: number;

	@ApiPropertyOptional({
		description: "IDs das imagens da galeria (referências para imagens já uploadadas)",
		example: [124, 125, 126],
		type: [Number],
	})
	@IsOptional()
	@IsArray()
	@IsNumber({}, { each: true })
	galleryImageIds?: number[];

	@ApiProperty({
		description: "Tipo da obra",
		enum: WorkType,
		example: WorkType.MANHWA,
	})
	@IsEnum(WorkType)
	type: WorkType;

	@ApiProperty({
		description: "Status da obra",
		enum: WorkStatus,
		example: WorkStatus.ONGOING,
	})
	@IsEnum(WorkStatus)
	status: WorkStatus;

	@ApiPropertyOptional({
		description: "Autor da obra",
		example: "Chugong",
		maxLength: 255,
	})
	@IsOptional()
	@IsString()
	@MaxLength(255)
	author?: string;

	@ApiPropertyOptional({
		description: "Artista da obra",
		example: "DUBU (REDICE STUDIO)",
		maxLength: 255,
	})
	@IsOptional()
	@IsString()
	@MaxLength(255)
	artist?: string;

	@ApiPropertyOptional({
		description: "Total de capítulos",
		example: 179,
		minimum: 1,
	})
	@IsOptional()
	@IsInt()
	@Min(1)
	totalChapters?: number;

	@ApiPropertyOptional({
		description: "Data de lançamento",
		example: "2018-07-25",
	})
	@IsOptional()
	@IsDateString()
	releaseDate?: Date;
}
