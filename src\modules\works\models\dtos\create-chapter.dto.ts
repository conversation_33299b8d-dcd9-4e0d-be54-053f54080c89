import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
	IsString,
	IsNotEmpty,
	IsOptional,
	IsInt,
	IsDateString,
	IsUUID,
	Min,
	MaxLength,
	IsUrl,
} from 'class-validator';

export class CreateChapterDto {
	@ApiProperty({
		description: 'ID da obra',
		example: '123e4567-e89b-12d3-a456-************',
	})
	@IsUUID()
	@IsNotEmpty()
	workId: string;

	@ApiProperty({
		description: 'Número do capítulo',
		example: 1,
		minimum: 1,
	})
	@IsInt()
	@Min(1)
	number: number;

	@ApiPropertyOptional({
		description: 'Título do capítulo',
		example: 'O Despertar',
		maxLength: 255,
	})
	@IsOptional()
	@IsString()
	@MaxLength(255)
	title?: string;

	@ApiPropertyOptional({
		description: 'Data de lançamento do capítulo',
		example: '2024-01-15',
	})
	@IsOptional()
	@IsDateString()
	releaseDate?: Date;

	@ApiPropertyOptional({
		description: 'URL do capítulo',
		example: 'https://example.com/chapter/1',
	})
	@IsOptional()
	@IsUrl()
	url?: string;
}
