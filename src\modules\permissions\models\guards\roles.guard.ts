import { Injectable, CanActivate, ExecutionContext, Inject } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { IUserRoleRepository } from "../interfaces";
import { UserRole as UserRoleEnum } from "../../../user/models/enums";
import { ROLES_KEY } from "../decorators/roles.decorator";

/**
 * Guard que verifica se o usuário possui os roles necessários
 * Funciona em conjunto com o decorator @Roles()
 */
@Injectable()
export class RolesGuard implements CanActivate {
	constructor(
		private reflector: Reflector,
		@Inject("IUserRoleRepository")
		private readonly userRoleRepository: IUserRoleRepository
	) {}

	async canActivate(context: ExecutionContext): Promise<boolean> {
		// Busca os roles requeridos definidos no decorator @Roles()
		const requiredRoles = this.reflector.getAllAndOverride<UserRoleEnum[]>(ROLES_KEY, [context.getHandler(), context.getClass()]);

		// Se não há roles requeridos, permite acesso
		if (!requiredRoles || requiredRoles.length === 0) {
			return true;
		}

		// Extrai o usuário da requisição
		const request = context.switchToHttp().getRequest();
		const user = request.user;

		// Se não há usuário autenticado, nega acesso
		if (!user || !user.id) {
			return false;
		}

		// Verifica se o usuário tem o role básico definido na entidade User
		const userBasicRole = user.role || UserRoleEnum.USER;

		// Se o role básico do usuário está na lista de roles requeridos, permite acesso
		if (requiredRoles.includes(userBasicRole)) {
			return true;
		}

		// Verifica roles adicionais através do sistema de permissões
		try {
			const userRoles = await this.userRoleRepository.findByUserId(user.id);
			const userRoleNames = userRoles
				.filter(ur => ur.isActive && (!ur.expiresAt || ur.expiresAt > new Date()))
				.map(ur => ur.role?.name)
				.filter(Boolean);

			// Mapeia nomes de roles para o enum UserRole
			const userRoleEnums = userRoleNames.map(roleName => this.mapRoleNameToEnum(roleName)).filter(Boolean);

			// Verifica se o usuário possui pelo menos um dos roles requeridos
			return requiredRoles.some(requiredRole => userRoleEnums.includes(requiredRole) || userBasicRole === requiredRole);
		} catch (error) {
			// Em caso de erro, nega acesso por segurança
			console.error("Erro ao verificar roles do usuário:", error);
			return false;
		}
	}

	/**
	 * Mapeia nomes de roles do banco para o enum UserRole
	 */
	private mapRoleNameToEnum(roleName: string): UserRoleEnum | null {
		const roleMapping: Record<string, UserRoleEnum> = {
			user: UserRoleEnum.USER,
			moderator: UserRoleEnum.MODERATOR,
			admin: UserRoleEnum.ADMIN,
			super_admin: UserRoleEnum.SUPER_ADMIN,
			content_moderator: UserRoleEnum.MODERATOR,
			works_editor: UserRoleEnum.MODERATOR,
			system_admin: UserRoleEnum.ADMIN,
		};

		return roleMapping[roleName.toLowerCase()] || null;
	}
}
