import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPermissionsSystem1703000000000 implements MigrationInterface {
	name = "AddPermissionsSystem1703000000000";

	public async up(queryRunner: QueryRunner): Promise<void> {
		// Criar enum para user roles
		await queryRunner.query(`
			DO $$ BEGIN
				CREATE TYPE "user_role_enum" AS ENUM('user', 'moderator', 'admin', 'super_admin');
			EXCEPTION
				WHEN duplicate_object THEN null;
			END $$;
		`);

		// Criar enum para permissions
		await queryRunner.query(`
			DO $$ BEGIN
				CREATE TYPE "permission_enum" AS ENUM(
					'users:create', 'users:read', 'users:update', 'users:delete', 'users:list', 'users:manage_roles',
					'works:create', 'works:read', 'works:update', 'works:delete', 'works:list', 'works:moderate',
					'reading:create', 'reading:read', 'reading:update', 'reading:delete', 'reading:list',
					'lists:create', 'lists:read', 'lists:update', 'lists:delete', 'lists:list', 'lists:moderate',
					'rankings:create', 'rankings:read', 'rankings:update', 'rankings:delete', 'rankings:list', 'rankings:moderate',
					'reviews:create', 'reviews:read', 'reviews:update', 'reviews:delete', 'reviews:list', 'reviews:moderate',
					'tags:create', 'tags:read', 'tags:update', 'tags:delete', 'tags:list', 'tags:moderate',
					'admin:dashboard', 'admin:system_config', 'admin:logs', 'admin:metrics', 'admin:backup',
					'moderate:content', 'moderate:users', 'moderate:reports',
					'permissions:create', 'permissions:read', 'permissions:update', 'permissions:delete', 'permissions:assign'
				);
			EXCEPTION
				WHEN duplicate_object THEN null;
			END $$;
		`);

		// Adicionar coluna role na tabela users
		await queryRunner.query(`
			ALTER TABLE "users" 
			ADD COLUMN IF NOT EXISTS "role" "user_role_enum" NOT NULL DEFAULT 'user'
		`);

		// Criar tabela permissions
		await queryRunner.query(`
			CREATE TABLE IF NOT EXISTS "permissions" (
				"id" uuid NOT NULL DEFAULT uuid_generate_v4(),
				"name" "permission_enum" NOT NULL,
				"description" character varying(255),
				"resource" character varying(100),
				"action" character varying(100),
				"isActive" boolean NOT NULL DEFAULT true,
				"createdAt" TIMESTAMP NOT NULL DEFAULT now(),
				"updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
				CONSTRAINT "PK_permissions" PRIMARY KEY ("id"),
				CONSTRAINT "UQ_permissions_name" UNIQUE ("name")
			)
		`);

		// Criar índice na coluna name da tabela permissions
		await queryRunner.query(`
			CREATE INDEX IF NOT EXISTS "IDX_permissions_name" ON "permissions" ("name")
		`);

		// Criar tabela roles
		await queryRunner.query(`
			CREATE TABLE IF NOT EXISTS "roles" (
				"id" uuid NOT NULL DEFAULT uuid_generate_v4(),
				"name" character varying(50) NOT NULL,
				"description" character varying(255),
				"isActive" boolean NOT NULL DEFAULT true,
				"isSystem" boolean NOT NULL DEFAULT false,
				"createdAt" TIMESTAMP NOT NULL DEFAULT now(),
				"updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
				CONSTRAINT "PK_roles" PRIMARY KEY ("id"),
				CONSTRAINT "UQ_roles_name" UNIQUE ("name")
			)
		`);

		// Criar índice na coluna name da tabela roles
		await queryRunner.query(`
			CREATE INDEX IF NOT EXISTS "IDX_roles_name" ON "roles" ("name")
		`);

		// Criar tabela user_roles
		await queryRunner.query(`
			CREATE TABLE IF NOT EXISTS "user_roles" (
				"id" uuid NOT NULL DEFAULT uuid_generate_v4(),
				"userId" integer NOT NULL,
				"roleId" uuid NOT NULL,
				"isActive" boolean NOT NULL DEFAULT true,
				"expiresAt" TIMESTAMP,
				"assignedBy" integer,
				"createdAt" TIMESTAMP NOT NULL DEFAULT now(),
				"updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
				CONSTRAINT "PK_user_roles" PRIMARY KEY ("id"),
				CONSTRAINT "UQ_user_roles_userId_roleId" UNIQUE ("userId", "roleId")
			)
		`);

		// Criar índices na tabela user_roles
		await queryRunner.query(`
			CREATE INDEX IF NOT EXISTS "IDX_user_roles_userId" ON "user_roles" ("userId")
		`);
		await queryRunner.query(`
			CREATE INDEX IF NOT EXISTS "IDX_user_roles_roleId" ON "user_roles" ("roleId")
		`);

		// Criar tabela role_permissions (many-to-many)
		await queryRunner.query(`
			CREATE TABLE IF NOT EXISTS "role_permissions" (
				"roleId" uuid NOT NULL,
				"permissionId" uuid NOT NULL,
				CONSTRAINT "PK_role_permissions" PRIMARY KEY ("roleId", "permissionId")
			)
		`);

		// Criar índices na tabela role_permissions
		await queryRunner.query(`
			CREATE INDEX IF NOT EXISTS "IDX_role_permissions_roleId" ON "role_permissions" ("roleId")
		`);
		await queryRunner.query(`
			CREATE INDEX IF NOT EXISTS "IDX_role_permissions_permissionId" ON "role_permissions" ("permissionId")
		`);

		// Adicionar foreign keys
		await queryRunner.query(`
			ALTER TABLE "user_roles" 
			ADD CONSTRAINT "FK_user_roles_roleId" 
			FOREIGN KEY ("roleId") REFERENCES "roles"("id") ON DELETE CASCADE
		`);

		await queryRunner.query(`
			ALTER TABLE "role_permissions" 
			ADD CONSTRAINT "FK_role_permissions_roleId" 
			FOREIGN KEY ("roleId") REFERENCES "roles"("id") ON DELETE CASCADE
		`);

		await queryRunner.query(`
			ALTER TABLE "role_permissions" 
			ADD CONSTRAINT "FK_role_permissions_permissionId" 
			FOREIGN KEY ("permissionId") REFERENCES "permissions"("id") ON DELETE CASCADE
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Remover foreign keys
		await queryRunner.query(`ALTER TABLE "role_permissions" DROP CONSTRAINT IF EXISTS "FK_role_permissions_permissionId"`);
		await queryRunner.query(`ALTER TABLE "role_permissions" DROP CONSTRAINT IF EXISTS "FK_role_permissions_roleId"`);
		await queryRunner.query(`ALTER TABLE "user_roles" DROP CONSTRAINT IF EXISTS "FK_user_roles_roleId"`);

		// Remover tabelas
		await queryRunner.query(`DROP TABLE IF EXISTS "role_permissions"`);
		await queryRunner.query(`DROP TABLE IF EXISTS "user_roles"`);
		await queryRunner.query(`DROP TABLE IF EXISTS "roles"`);
		await queryRunner.query(`DROP TABLE IF EXISTS "permissions"`);

		// Remover coluna role da tabela users
		await queryRunner.query(`ALTER TABLE "users" DROP COLUMN IF EXISTS "role"`);

		// Remover enums
		await queryRunner.query(`DROP TYPE IF EXISTS "permission_enum"`);
		await queryRunner.query(`DROP TYPE IF EXISTS "user_role_enum"`);
	}
}
