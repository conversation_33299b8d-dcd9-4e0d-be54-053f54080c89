import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn, Index } from "typeorm";
import { Review } from "./review.entity";

@Entity("review_reactions")
@Index(["reviewId", "userId"], { unique: true }) // Um usuário só pode reagir uma vez a cada review
export class ReviewReaction {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "uuid" })
	@Index()
	reviewId: string;

	@Column({ type: "integer" })
	@Index()
	userId: number;

	@Column({ type: "boolean" })
	isLike: boolean;

	@ManyToOne(() => Review, review => review.reactions, { onDelete: "CASCADE" })
	@JoinColumn({ name: "reviewId" })
	review: Review;

	@CreateDateColumn()
	createdAt: Date;
}
