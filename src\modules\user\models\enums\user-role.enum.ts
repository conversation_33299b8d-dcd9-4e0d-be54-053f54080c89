/**
 * Enum que define os diferentes tipos de roles de usuário no sistema
 *
 * @enum {string}
 */
export enum UserRole {
	/**
	 * Usuário comum do sistema
	 * - Pode gerenciar suas próprias listas, rankings e leituras
	 * - Pode criar reviews e comentários
	 * - Acesso limitado às funcionalidades básicas
	 */
	USER = "user",

	/**
	 * Moderador do sistema
	 * - <PERSON>das as permissões de USER
	 * - Pode moderar reviews e comentários
	 * - Pode gerenciar tags e categorias
	 * - Pode suspender usuários temporariamente
	 */
	MODERATOR = "moderator",

	/**
	 * Administrador do sistema
	 * - <PERSON>das as permissões de MODERATOR
	 * - Pode gerenciar usuários (criar, editar, deletar)
	 * - Pode gerenciar obras (criar, editar, deletar)
	 * - Pode gerenciar roles e permissões
	 * - Acesso total ao sistema
	 */
	ADMIN = "admin",

	/**
	 * Super Administrador
	 * - <PERSON><PERSON> as permissões do sistema
	 * - Pode gerenciar outros administradores
	 * - Pode acessar configurações do sistema
	 * - Acesso irrestrito
	 */
	SUPER_ADMIN = "super_admin",
}
