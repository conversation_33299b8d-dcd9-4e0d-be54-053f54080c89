import { MigrationInterface, QueryRunner } from "typeorm";

export class SeedPermissionsAndRoles1703000000001 implements MigrationInterface {
	name = "SeedPermissionsAndRoles1703000000001";

	public async up(queryRunner: QueryRunner): Promise<void> {
		// Inserir todas as permissões
		await queryRunner.query(`
			INSERT INTO "permissions" ("name", "description", "resource", "action") VALUES
			-- Usu<PERSON>rios
			('users:create', 'Criar novos usuários', 'users', 'create'),
			('users:read', 'Visualizar informações de usuários', 'users', 'read'),
			('users:update', 'Atualizar dados de usuários', 'users', 'update'),
			('users:delete', 'Deletar usuários', 'users', 'delete'),
			('users:list', 'Listar usuários', 'users', 'list'),
			('users:manage_roles', 'Gerenciar roles de usuários', 'users', 'manage_roles'),
			
			-- <PERSON><PERSON><PERSON>
			('works:create', 'Criar novas obras', 'works', 'create'),
			('works:read', 'Visualizar obras', 'works', 'read'),
			('works:update', 'Atualizar obras', 'works', 'update'),
			('works:delete', 'Deletar obras', 'works', 'delete'),
			('works:list', 'Listar obras', 'works', 'list'),
			('works:moderate', 'Moderar obras', 'works', 'moderate'),
			
			-- Leitura
			('reading:create', 'Adicionar obras à lista de leitura', 'reading', 'create'),
			('reading:read', 'Visualizar progresso de leitura', 'reading', 'read'),
			('reading:update', 'Atualizar progresso de leitura', 'reading', 'update'),
			('reading:delete', 'Remover obras da lista de leitura', 'reading', 'delete'),
			('reading:list', 'Listar histórico de leitura', 'reading', 'list'),
			
			-- Listas
			('lists:create', 'Criar listas personalizadas', 'lists', 'create'),
			('lists:read', 'Visualizar listas', 'lists', 'read'),
			('lists:update', 'Atualizar listas', 'lists', 'update'),
			('lists:delete', 'Deletar listas', 'lists', 'delete'),
			('lists:list', 'Listar todas as listas', 'lists', 'list'),
			('lists:moderate', 'Moderar listas públicas', 'lists', 'moderate'),
			
			-- Rankings
			('rankings:create', 'Criar rankings', 'rankings', 'create'),
			('rankings:read', 'Visualizar rankings', 'rankings', 'read'),
			('rankings:update', 'Atualizar rankings', 'rankings', 'update'),
			('rankings:delete', 'Deletar rankings', 'rankings', 'delete'),
			('rankings:list', 'Listar rankings', 'rankings', 'list'),
			('rankings:moderate', 'Moderar rankings públicos', 'rankings', 'moderate'),
			
			-- Reviews
			('reviews:create', 'Criar avaliações', 'reviews', 'create'),
			('reviews:read', 'Visualizar avaliações', 'reviews', 'read'),
			('reviews:update', 'Atualizar avaliações', 'reviews', 'update'),
			('reviews:delete', 'Deletar avaliações', 'reviews', 'delete'),
			('reviews:list', 'Listar avaliações', 'reviews', 'list'),
			('reviews:moderate', 'Moderar avaliações', 'reviews', 'moderate'),
			
			-- Tags
			('tags:create', 'Criar tags', 'tags', 'create'),
			('tags:read', 'Visualizar tags', 'tags', 'read'),
			('tags:update', 'Atualizar tags', 'tags', 'update'),
			('tags:delete', 'Deletar tags', 'tags', 'delete'),
			('tags:list', 'Listar tags', 'tags', 'list'),
			('tags:moderate', 'Moderar tags', 'tags', 'moderate'),
			
			-- Administração
			('admin:dashboard', 'Acessar dashboard administrativo', 'admin', 'dashboard'),
			('admin:system_config', 'Configurar sistema', 'admin', 'system_config'),
			('admin:logs', 'Visualizar logs do sistema', 'admin', 'logs'),
			('admin:metrics', 'Visualizar métricas do sistema', 'admin', 'metrics'),
			('admin:backup', 'Gerenciar backups', 'admin', 'backup'),
			
			-- Moderação
			('moderate:content', 'Moderar conteúdo geral', 'moderate', 'content'),
			('moderate:users', 'Moderar usuários', 'moderate', 'users'),
			('moderate:reports', 'Gerenciar denúncias', 'moderate', 'reports'),
			
			-- Permissões
			('permissions:create', 'Criar permissões', 'permissions', 'create'),
			('permissions:read', 'Visualizar permissões', 'permissions', 'read'),
			('permissions:update', 'Atualizar permissões', 'permissions', 'update'),
			('permissions:delete', 'Deletar permissões', 'permissions', 'delete'),
			('permissions:assign', 'Atribuir permissões', 'permissions', 'assign')
			ON CONFLICT ("name") DO NOTHING;
		`);

		// Criar roles básicos
		await queryRunner.query(`
			INSERT INTO "roles" ("name", "description", "isSystem") VALUES
			('user', 'Usuário comum do sistema', true),
			('moderator', 'Moderador de conteúdo', true),
			('admin', 'Administrador do sistema', true),
			('super_admin', 'Super administrador', true)
			ON CONFLICT ("name") DO NOTHING;
		`);

		// Atribuir permissões aos roles
		// Role: user (permissões básicas)
		await queryRunner.query(`
			INSERT INTO "role_permissions" ("roleId", "permissionId")
			SELECT r.id, p.id
			FROM "roles" r, "permissions" p
			WHERE r.name = 'user' AND p.name IN (
				'works:read', 'works:list',
				'reading:create', 'reading:read', 'reading:update', 'reading:delete', 'reading:list',
				'lists:create', 'lists:read', 'lists:update', 'lists:delete',
				'rankings:create', 'rankings:read', 'rankings:update', 'rankings:delete',
				'reviews:create', 'reviews:read', 'reviews:update', 'reviews:delete',
				'tags:read', 'tags:list'
			)
			ON CONFLICT DO NOTHING;
		`);

		// Role: moderator (permissões de moderação)
		await queryRunner.query(`
			INSERT INTO "role_permissions" ("roleId", "permissionId")
			SELECT r.id, p.id
			FROM "roles" r, "permissions" p
			WHERE r.name = 'moderator' AND p.name IN (
				-- Todas as permissões de user
				'works:read', 'works:list', 'works:moderate',
				'reading:create', 'reading:read', 'reading:update', 'reading:delete', 'reading:list',
				'lists:create', 'lists:read', 'lists:update', 'lists:delete', 'lists:moderate',
				'rankings:create', 'rankings:read', 'rankings:update', 'rankings:delete', 'rankings:moderate',
				'reviews:create', 'reviews:read', 'reviews:update', 'reviews:delete', 'reviews:moderate',
				'tags:create', 'tags:read', 'tags:update', 'tags:delete', 'tags:list', 'tags:moderate',
				-- Permissões específicas de moderação
				'moderate:content', 'moderate:reports',
				'users:read', 'users:list'
			)
			ON CONFLICT DO NOTHING;
		`);

		// Role: admin (permissões administrativas)
		await queryRunner.query(`
			INSERT INTO "role_permissions" ("roleId", "permissionId")
			SELECT r.id, p.id
			FROM "roles" r, "permissions" p
			WHERE r.name = 'admin' AND p.name IN (
				-- Todas as permissões de moderator
				'works:create', 'works:read', 'works:update', 'works:delete', 'works:list', 'works:moderate',
				'reading:create', 'reading:read', 'reading:update', 'reading:delete', 'reading:list',
				'lists:create', 'lists:read', 'lists:update', 'lists:delete', 'lists:list', 'lists:moderate',
				'rankings:create', 'rankings:read', 'rankings:update', 'rankings:delete', 'rankings:list', 'rankings:moderate',
				'reviews:create', 'reviews:read', 'reviews:update', 'reviews:delete', 'reviews:list', 'reviews:moderate',
				'tags:create', 'tags:read', 'tags:update', 'tags:delete', 'tags:list', 'tags:moderate',
				-- Permissões administrativas
				'users:create', 'users:read', 'users:update', 'users:delete', 'users:list', 'users:manage_roles',
				'admin:dashboard', 'admin:logs', 'admin:metrics',
				'moderate:content', 'moderate:users', 'moderate:reports',
				'permissions:read', 'permissions:assign'
			)
			ON CONFLICT DO NOTHING;
		`);

		// Role: super_admin (todas as permissões)
		await queryRunner.query(`
			INSERT INTO "role_permissions" ("roleId", "permissionId")
			SELECT r.id, p.id
			FROM "roles" r, "permissions" p
			WHERE r.name = 'super_admin'
			ON CONFLICT DO NOTHING;
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Remover todas as associações role-permission
		await queryRunner.query(`DELETE FROM "role_permissions"`);
		
		// Remover roles do sistema
		await queryRunner.query(`DELETE FROM "roles" WHERE "isSystem" = true`);
		
		// Remover todas as permissões
		await queryRunner.query(`DELETE FROM "permissions"`);
	}
}
