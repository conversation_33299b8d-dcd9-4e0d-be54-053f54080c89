import { Injectable, Inject } from "@nestjs/common";
import { DuplicateResourceException, ValidationException, UnauthorizedOperationException } from "src/shared/exceptions/business.exceptions";
import { ICreateWorkRequest, IWork, IWorkRepository } from "../../models/interfaces";
import { IImageRepository } from "../../../images/models/interfaces";
import { UserRole } from "../../../user/models/enums";

@Injectable()
export class CreateWorkUseCase {
	constructor(
		@Inject("IWorkRepository")
		private readonly workRepository: IWorkRepository,
		@Inject("IImageRepository")
		private readonly imageRepository: IImageRepository
	) {}

	async execute(data: ICreateWorkRequest, userId?: number, userRole?: UserRole): Promise<IWork> {
		// Verificar se já existe uma obra com o mesmo título
		const existingWork = await this.workRepository.findByTitle(data.title);
		if (existingWork) {
			throw new DuplicateResourceException("Obra", "title", data.title);
		}

		// Validar imagem de capa se fornecida
		if (data.coverImageId) {
			await this.validateImageAccess(data.coverImageId, userId, userRole);
		}

		// Validar imagens da galeria se fornecidas
		if (data.galleryImageIds && data.galleryImageIds.length > 0) {
			await Promise.all(data.galleryImageIds.map(imageId => this.validateImageAccess(imageId, userId, userRole)));
		}

		// Criar a obra
		const work = await this.workRepository.create(data);

		// Associar imagens se fornecidas
		if (data.coverImageId) {
			await this.workRepository.associateCoverImage(work.id, data.coverImageId);
		}

		if (data.galleryImageIds && data.galleryImageIds.length > 0) {
			await this.workRepository.associateGalleryImages(work.id, data.galleryImageIds);
		}

		// Retornar a obra com as imagens associadas
		return (await this.workRepository.findByIdWithImages(work.id)) || work;
	}

	private async validateImageAccess(imageId: number, userId?: number, userRole?: UserRole): Promise<void> {
		const image = await this.imageRepository.findById(imageId);

		if (!image) {
			throw new ValidationException({
				imageId: [`Imagem com ID ${imageId} não encontrada`],
			});
		}

		if (!image.isAvailable()) {
			throw new ValidationException({
				imageId: [`Imagem com ID ${imageId} não está disponível`],
			});
		}

		// Validar propriedade (apenas proprietário ou admin pode usar)
		const isAdmin = userRole === UserRole.ADMIN;
		const isOwner = userId && image.uploadedBy === userId;

		if (!isAdmin && !isOwner) {
			throw new UnauthorizedOperationException(`Você não tem permissão para usar a imagem ${imageId}`);
		}
	}
}
