export interface IList {
	id: string;
	userId: number;
	name: string;
	description?: string;
	isPublic: boolean;
	coverImage?: string;
	itemsCount: number;
	createdAt: Date;
	updatedAt: Date;
}

export interface ICreateListRequest {
	userId: number;
	name: string;
	description?: string;
	isPublic?: boolean;
	coverImage?: string;
}

export interface IUpdateListRequest {
	name?: string;
	description?: string;
	isPublic?: boolean;
	coverImage?: string;
}

export interface IListFilters {
	userId: number;
	isPublic?: boolean;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: "name" | "createdAt" | "updatedAt" | "itemsCount";
	sortOrder?: "ASC" | "DESC";
}

export interface IListRepository {
	create(data: ICreateListRequest): Promise<IList>;
	findById(id: string): Promise<IList | null>;
	findByUserAndName(userId: number, name: string): Promise<IList | null>;
	findByUser(filters: IListFilters): Promise<{ lists: IList[]; total: number }>;
	update(id: string, data: IUpdateListRequest): Promise<IList>;
	delete(id: string): Promise<void>;
	incrementItemsCount(listId: string): Promise<void>;
	decrementItemsCount(listId: string): Promise<void>;
	countByUserId(userId: number): Promise<number>;
	countPublicByUserId(userId: number): Promise<number>;
}
