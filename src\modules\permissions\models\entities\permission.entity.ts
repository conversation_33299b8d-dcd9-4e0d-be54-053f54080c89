import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToMany, Index } from "typeorm";
import { Permission } from "../enums";
import { Role } from "./role.entity";

/**
 * Entidade que representa uma permissão no sistema
 *
 * @entity permissions
 */
@Entity("permissions")
export class PermissionEntity {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({
		type: "enum",
		enum: Permission,
		unique: true,
	})
	@Index()
	name: Permission;

	@Column({ length: 255, nullable: true })
	description?: string;

	@Column({ length: 100, nullable: true })
	resource?: string; // Ex: "users", "works", "lists"

	@Column({ length: 100, nullable: true })
	action?: string; // Ex: "create", "read", "update", "delete"

	@Column({ default: true })
	isActive: boolean;

	@ManyToMany(() => Role, role => role.permissions)
	roles: Role[];

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;
}
