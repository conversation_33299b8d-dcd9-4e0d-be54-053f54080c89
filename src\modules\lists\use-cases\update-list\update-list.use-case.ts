import { Inject, Injectable } from "@nestjs/common";
import { DuplicateResourceException, ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IList, IListRepository, IUpdateListRequest } from "../../models/interfaces";

@Injectable()
export class UpdateListUseCase {
	constructor(
		@Inject("IListRepository")
		private readonly listRepository: IListRepository
	) {}

	async execute(userId: number, listId: string, data: IUpdateListRequest): Promise<IList> {
		// Verificar se a lista existe e pertence ao usuário
		const existingList = await this.listRepository.findById(listId);
		if (!existingList) {
			throw new ResourceNotFoundException("Lista", listId);
		}
		if (existingList.userId !== userId) {
			throw new ResourceNotFoundException("Lista do usuário", `${listId}:${userId}`);
		}

		// Se está tentando alterar o nome, verificar se não existe outra lista com o mesmo nome
		if (data.name && data.name !== existingList.name) {
			const listWithSameName = await this.listRepository.findByUserAndName(userId, data.name);
			if (listWithSameName) {
				throw new DuplicateResourceException("Lista", "name", data.name);
			}
		}

		return await this.listRepository.update(listId, data);
	}
}
