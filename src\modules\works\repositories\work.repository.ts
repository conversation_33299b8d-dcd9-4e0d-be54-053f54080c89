import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, SelectQueryBuilder } from "typeorm";
import { Work } from "../models/entities";
import { IWorkRepository, ICreateWorkRequest, IUpdateWorkRequest, IWorkFilters, IWork } from "../models/interfaces";
import { Image } from "src/modules/images";

@Injectable()
export class WorkRepository implements IWorkRepository {
	constructor(
		@InjectRepository(Work)
		private readonly workRepository: Repository<Work>
	) {}

	async create(data: ICreateWorkRequest): Promise<IWork> {
		const work = this.workRepository.create(data);
		return await this.workRepository.save(work);
	}

	async findById(id: string): Promise<IWork | null> {
		return await this.workRepository.findOne({
			where: { id },
			relations: ["chapters"],
		});
	}

	async findByIdWithImages(id: string): Promise<IWork | null> {
		return await this.workRepository.findOne({
			where: { id },
			relations: ["chapters", "coverImageEntity", "galleryImages"],
		});
	}

	async findByTitle(title: string): Promise<IWork | null> {
		return await this.workRepository.findOne({
			where: { title },
		});
	}

	async findAll(filters: IWorkFilters): Promise<{ works: IWork[]; total: number }> {
		const queryBuilder = this.workRepository.createQueryBuilder("work");

		this.applyFilters(queryBuilder, filters);
		this.applySorting(queryBuilder, filters);

		const total = await queryBuilder.getCount();

		const { page = 1, limit = 20 } = filters;
		const skip = (page - 1) * limit;

		queryBuilder.skip(skip).take(limit);

		const works = await queryBuilder.getMany();

		return { works, total };
	}

	async update(id: string, data: IUpdateWorkRequest): Promise<IWork> {
		await this.workRepository.update(id, data);
		const updatedWork = await this.findById(id);
		if (!updatedWork) {
			throw new Error("Work not found after update");
		}
		return updatedWork;
	}

	async delete(id: string): Promise<void> {
		await this.workRepository.delete(id);
	}

	async associateCoverImage(workId: string, imageId: number): Promise<void> {
		await this.workRepository.update(workId, { coverImageId: imageId });
	}

	async associateGalleryImages(workId: string, imageIds: number[]): Promise<void> {
		const work = await this.workRepository.findOne({
			where: { id: workId },
			relations: ["galleryImages"],
		});

		if (!work) {
			throw new Error("Work not found");
		}

		// Buscar as imagens pelos IDs
		const imageRepository = this.workRepository.manager.getRepository("Image");
		const images = (await imageRepository.findByIds(imageIds)) as Image[];

		// Associar as imagens à obra
		work.galleryImages = [...(work.galleryImages || []), ...images];
		await this.workRepository.save(work);
	}

	async removeCoverImage(workId: string): Promise<void> {
		await this.workRepository.update(workId, { coverImageId: null });
	}

	async removeGalleryImage(workId: string, imageId: number): Promise<void> {
		const work = await this.workRepository.findOne({
			where: { id: workId },
			relations: ["galleryImages"],
		});

		if (!work) {
			throw new Error("Work not found");
		}

		// Remover a imagem específica da galeria
		work.galleryImages = work.galleryImages?.filter(img => img.id !== imageId) || [];
		await this.workRepository.save(work);
	}

	async removeAllGalleryImages(workId: string): Promise<void> {
		const work = await this.workRepository.findOne({
			where: { id: workId },
			relations: ["galleryImages"],
		});

		if (!work) {
			throw new Error("Work not found");
		}

		work.galleryImages = [];
		await this.workRepository.save(work);
	}

	async updateAverageRating(id: string): Promise<void> {
		// Esta implementação será completada quando criarmos o módulo de reviews
		// Por enquanto, apenas um placeholder
		await this.workRepository.update(id, { averageRating: 0 });
	}

	private applyFilters(queryBuilder: SelectQueryBuilder<Work>, filters: IWorkFilters): void {
		if (filters.type) {
			queryBuilder.andWhere("work.type = :type", { type: filters.type });
		}

		if (filters.status) {
			queryBuilder.andWhere("work.status = :status", { status: filters.status });
		}

		if (filters.author) {
			queryBuilder.andWhere("work.author ILIKE :author", {
				author: `%${filters.author}%`,
			});
		}

		if (filters.artist) {
			queryBuilder.andWhere("work.artist ILIKE :artist", {
				artist: `%${filters.artist}%`,
			});
		}

		if (filters.search) {
			queryBuilder.andWhere("(work.title ILIKE :search OR work.description ILIKE :search)", {
				search: `%${filters.search}%`,
			});
		}

		if (filters.minRating !== undefined) {
			queryBuilder.andWhere("work.averageRating >= :minRating", {
				minRating: filters.minRating,
			});
		}

		if (filters.maxRating !== undefined) {
			queryBuilder.andWhere("work.averageRating <= :maxRating", {
				maxRating: filters.maxRating,
			});
		}

		// Tags filter será implementado quando criarmos o módulo de tags
		if (filters.tags && filters.tags.length > 0) {
			// Placeholder para filtro de tags
		}
	}

	private applySorting(queryBuilder: SelectQueryBuilder<Work>, filters: IWorkFilters): void {
		const { sortBy = "createdAt", sortOrder = "DESC" } = filters;
		queryBuilder.orderBy(`work.${sortBy}`, sortOrder);
	}
}
