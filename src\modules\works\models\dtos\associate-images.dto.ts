import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNumber, IsArray, IsOptional, IsNotEmpty } from "class-validator";

/**
 * DTO para associar imagem de capa a uma obra
 * Seguindo princípios SOLID - Single Responsibility
 */
export class AssociateCoverImageDto {
	@ApiProperty({
		description: "ID da imagem a ser associada como capa",
		example: 123,
	})
	@IsNumber()
	@IsNotEmpty()
	imageId: number;
}

/**
 * DTO para associar múltiplas imagens à galeria de uma obra
 * Seguindo princípios SOLID - Single Responsibility
 */
export class AssociateGalleryImagesDto {
	@ApiProperty({
		description: "IDs das imagens a serem associadas à galeria",
		example: [124, 125, 126],
		type: [Number],
	})
	@IsArray()
	@IsNumber({}, { each: true })
	@IsNotEmpty()
	imageIds: number[];
}

/**
 * DTO para remover imagem da galeria
 * Seguindo princípios SOLID - Single Responsibility
 */
export class RemoveGalleryImageDto {
	@ApiProperty({
		description: "ID da imagem a ser removida da galeria",
		example: 124,
	})
	@IsNumber()
	@IsNotEmpty()
	imageId: number;
}

/**
 * DTO para resposta de associação de imagens
 * Seguindo princípios SOLID - Single Responsibility
 */
export class ImageAssociationResponseDto {
	@ApiProperty({
		description: "Indica se a operação foi bem-sucedida",
		example: true,
	})
	success: boolean;

	@ApiProperty({
		description: "Mensagem descritiva da operação",
		example: "Imagem associada com sucesso",
	})
	message: string;

	@ApiPropertyOptional({
		description: "ID da obra afetada",
		example: "123e4567-e89b-12d3-a456-426614174000",
	})
	workId?: string;

	@ApiPropertyOptional({
		description: "IDs das imagens afetadas",
		example: [123, 124, 125],
		type: [Number],
	})
	imageIds?: number[];
}
