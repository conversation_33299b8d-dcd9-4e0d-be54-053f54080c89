import { MigrationInterface, QueryRunner, Table } from "typeorm";

export class AddImagesSchema1735117221000 implements MigrationInterface {
	name = "AddImagesSchema1735117221000";

	public async up(queryRunner: QueryRunner): Promise<void> {
		// Criar tabela de imagens
		await queryRunner.createTable(
			new Table({
				name: "images",
				columns: [
					{
						name: "id",
						type: "serial",
						isPrimary: true,
					},
					{
						name: "filename",
						type: "varchar",
						length: "255",
						isNullable: false,
					},
					{
						name: "originalName",
						type: "varchar",
						length: "255",
						isNullable: false,
					},
					{
						name: "path",
						type: "varchar",
						length: "500",
						isNullable: false,
					},
					{
						name: "url",
						type: "varchar",
						length: "500",
						isNullable: false,
					},
					{
						name: "mimeType",
						type: "varchar",
						length: "100",
						isNullable: false,
					},
					{
						name: "size",
						type: "bigint",
						isNullable: false,
					},
					{
						name: "width",
						type: "integer",
						isNullable: true,
					},
					{
						name: "height",
						type: "integer",
						isNullable: true,
					},
					{
						name: "status",
						type: "varchar",
						length: "20",
						default: "'active'",
						isNullable: false,
					},
					{
						name: "hash",
						type: "varchar",
						length: "64",
						isNullable: true,
					},
					{
						name: "metadata",
						type: "jsonb",
						isNullable: true,
					},
					{
						name: "uploadedBy",
						type: "integer",
						isNullable: false,
					},
					{
						name: "createdAt",
						type: "timestamp",
						default: "CURRENT_TIMESTAMP",
						isNullable: false,
					},
					{
						name: "updatedAt",
						type: "timestamp",
						default: "CURRENT_TIMESTAMP",
						isNullable: false,
					},
					{
						name: "deletedAt",
						type: "timestamp",
						isNullable: true,
					},
				],
			}),
			true
		);

		// Criar índices para a tabela de imagens
		await queryRunner.query(`CREATE INDEX "IDX_images_uploadedBy" ON "images" ("uploadedBy")`);
		await queryRunner.query(`CREATE INDEX "IDX_images_status" ON "images" ("status")`);
		await queryRunner.query(`CREATE INDEX "IDX_images_hash" ON "images" ("hash")`);
		await queryRunner.query(`CREATE INDEX "IDX_images_createdAt" ON "images" ("createdAt")`);

		// Criar foreign key para uploadedBy
		await queryRunner.query(`
			ALTER TABLE "images"
			ADD CONSTRAINT "FK_images_uploadedBy"
			FOREIGN KEY ("uploadedBy") REFERENCES "users"("id") ON DELETE CASCADE
		`);

		// Adicionar coluna coverImageId na tabela works
		await queryRunner.query(`
			ALTER TABLE "works" 
			ADD COLUMN "coverImageId" integer NULL
		`);

		// Criar foreign key para coverImageId
		await queryRunner.query(`
			ALTER TABLE "works"
			ADD CONSTRAINT "FK_works_coverImageId"
			FOREIGN KEY ("coverImageId") REFERENCES "images"("id") ON DELETE SET NULL
		`);

		// Criar tabela de junção para galeria de imagens
		await queryRunner.createTable(
			new Table({
				name: "work_images",
				columns: [
					{
						name: "workId",
						type: "uuid",
						isPrimary: true,
					},
					{
						name: "imageId",
						type: "integer",
						isPrimary: true,
					},
				],
			}),
			true
		);

		// Criar foreign keys para a tabela de junção
		await queryRunner.query(`
			ALTER TABLE "work_images"
			ADD CONSTRAINT "FK_work_images_workId"
			FOREIGN KEY ("workId") REFERENCES "works"("id") ON DELETE CASCADE
		`);

		await queryRunner.query(`
			ALTER TABLE "work_images"
			ADD CONSTRAINT "FK_work_images_imageId"
			FOREIGN KEY ("imageId") REFERENCES "images"("id") ON DELETE CASCADE
		`);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Remover tabela de junção
		await queryRunner.dropTable("work_images");

		// Remover coluna coverImageId da tabela works
		await queryRunner.query(`
			ALTER TABLE "works" 
			DROP COLUMN "coverImageId"
		`);

		// Remover tabela de imagens
		await queryRunner.dropTable("images");
	}
}
