import { ApiProperty } from "@nestjs/swagger";
import { Permission } from "../enums";

/**
 * DTO de resposta para um role
 */
export class RoleResponseDto {
	@ApiProperty({
		description: "ID único do role",
		example: "550e8400-e29b-41d4-a716-************",
		format: "uuid",
	})
	id: string;

	@ApiProperty({
		description: "Nome único do role",
		example: "content_moderator",
	})
	name: string;

	@ApiProperty({
		description: "Descrição do role",
		example: "Moderador responsável por revisar e aprovar conteúdo",
		nullable: true,
	})
	description?: string;

	@ApiProperty({
		description: "Se o role está ativo",
		example: true,
	})
	isActive: boolean;

	@ApiProperty({
		description: "Se é um role do sistema (não pode ser deletado)",
		example: false,
	})
	isSystem: boolean;

	@ApiProperty({
		description: "Data de criação do role",
		example: "2024-06-10T08:00:00.000Z",
		format: "date-time",
	})
	createdAt: Date;

	@ApiProperty({
		description: "Data da última atualização",
		example: "2024-06-12T10:30:00.000Z",
		format: "date-time",
	})
	updatedAt: Date;
}

/**
 * DTO de resposta para um role com suas permissões
 */
export class RoleWithPermissionsResponseDto extends RoleResponseDto {
	@ApiProperty({
		description: "Lista de permissões do role",
		example: ["users:read", "works:create", "reviews:moderate"],
		type: [String],
		enum: Permission,
	})
	permissions: Permission[];
}
