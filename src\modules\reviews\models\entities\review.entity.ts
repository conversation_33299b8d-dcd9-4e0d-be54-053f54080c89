import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, <PERSON>umn, CreateDateColumn, UpdateDateColumn, OneToMany, Index } from "typeorm";
import { ReviewComment } from "./review-comment.entity";
import { ReviewReaction } from "./review-reaction.entity";

@Entity("reviews")
@Index(["userId", "workId"], { unique: true }) // Um usuário só pode ter uma review para cada obra
export class Review {
	@PrimaryGeneratedColumn("uuid")
	id: string;

	@Column({ type: "integer" })
	@Index()
	userId: number;

	@Column({ type: "uuid" })
	@Index()
	workId: string;

	@Column({ type: "numeric", precision: 3, scale: 2 })
	@Index()
	rating: number;

	@Column({ type: "varchar", length: 100, nullable: true })
	title?: string;

	@Column({ type: "text" })
	content: string;

	@Column({ type: "integer", default: 0 })
	likes: number;

	@Column({ type: "integer", default: 0 })
	dislikes: number;

	@Column({ type: "boolean", default: true })
	isPublic: boolean;

	@Column({ type: "boolean", default: false })
	hasContainsSpoilers: boolean;

	@OneToMany(() => ReviewComment, comment => comment.review, { cascade: true })
	comments: ReviewComment[];

	@OneToMany(() => ReviewReaction, reaction => reaction.review, { cascade: true })
	reactions: ReviewReaction[];

	@CreateDateColumn()
	createdAt: Date;

	@UpdateDateColumn()
	updatedAt: Date;
}
