import { Inject, Injectable } from "@nestjs/common";
import * as bcrypt from "bcryptjs";
import { ICurrentUserPayload } from "../../../../shared/decorators/current-user.decorator";
import {
	BusinessException,
	DatabaseException,
	InvalidCredentialsException,
	ResourceNotFoundException,
} from "../../../../shared/exceptions/business.exceptions";
import { ChangePasswordDto } from "../../models/dtos/change-password.dto";
import { IAuthRepository } from "../../models/interfaces/auth-repository.interface";

@Injectable()
export class ChangePasswordUseCase {
	constructor(
		@Inject("IAuthRepository")
		private readonly authRepository: IAuthRepository
	) {}

	async execute(currentUser: ICurrentUserPayload, changePasswordDto: ChangePasswordDto): Promise<{ message: string }> {
		try {
			// Buscar o usuário atual
			const user = await this.authRepository.findByUsernameOrEmail(currentUser.username);
			if (!user) {
				throw new ResourceNotFoundException("<PERSON>u<PERSON><PERSON>", currentUser.username);
			}

			// Verificar se a senha atual está correta
			const isCurrentPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, user.password);
			if (!isCurrentPasswordValid) {
				throw new InvalidCredentialsException("senha atual");
			}

			// Verificar se a nova senha é diferente da atual
			const isSamePassword = await bcrypt.compare(changePasswordDto.newPassword, user.password);
			if (isSamePassword) {
				throw new BusinessException("A nova senha deve ser diferente da senha atual", 400, "SAME_PASSWORD");
			}

			// Criptografar a nova senha
			const saltRounds = 10;
			const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, saltRounds);

			// Atualizar a senha no banco de dados
			await this.authRepository.updatePassword(user.id, hashedNewPassword);

			// Invalidar todos os refresh tokens existentes por segurança
			await this.authRepository.updateRefreshToken(user.id, null);

			return {
				message: "Senha alterada com sucesso. Faça login novamente com a nova senha.",
			};
		} catch (error) {
			if (error instanceof ResourceNotFoundException || error instanceof InvalidCredentialsException || error instanceof BusinessException) {
				throw error;
			}
			throw new DatabaseException("alteração de senha", error);
		}
	}
}
