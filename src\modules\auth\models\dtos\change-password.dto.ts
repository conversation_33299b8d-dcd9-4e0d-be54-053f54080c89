import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString, Min<PERSON>ength, MaxLength } from "class-validator";

export class ChangePasswordDto {
	@ApiProperty({
		description: "Senha atual do usuário",
		example: "MinhaSenh@123",
		type: "string",
		format: "password",
		minLength: 6,
		maxLength: 128,
	})
	@IsNotEmpty({ message: "Senha atual é obrigatória" })
	@IsString({ message: "Senha atual deve ser uma string" })
	currentPassword: string;

	@ApiProperty({
		description: "Nova senha do usuário",
		example: "NovaSenha@456",
		type: "string",
		format: "password",
		minLength: 6,
		maxLength: 128,
	})
	@IsNotEmpty({ message: "Nova senha é obrigatória" })
	@IsString({ message: "Nova senha deve ser uma string" })
	@MinLength(6, { message: "Nova senha deve ter pelo menos 6 caracteres" })
	@MaxLength(128, { message: "Nova senha deve ter no máximo 128 caracteres" })
	newPassword: string;
}
