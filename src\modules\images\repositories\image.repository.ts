import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Like, Between } from "typeorm";
import { Image } from "../models/entities/image.entity";
import { IImageRepository } from "../models/interfaces/image-repository.interface";
import { ImageStatus } from "../models/enums";
import { FindAllOptions, PaginatedResult } from "../../../shared/interfaces/base-repository.interface";

/**
 * Repositório para entidade Image
 * Seguindo princípios SOLID - Single Responsibility e Dependency Inversion
 */
@Injectable()
export class ImageRepository implements IImageRepository {
	constructor(
		@InjectRepository(Image)
		private readonly repository: Repository<Image>
	) {}

	async create(data: Partial<Image>): Promise<Image> {
		const image = this.repository.create(data);
		return await this.repository.save(image);
	}

	async findById(id: number): Promise<Image | null> {
		return await this.repository.findOne({
			where: { id },
			relations: ["uploader"],
		});
	}

	async findAll(options?: FindAllOptions<Image>): Promise<PaginatedResult<Image>> {
		const page = options?.page || 1;
		const limit = options?.limit || 10;
		const skip = (page - 1) * limit;

		const [images, total] = await this.repository.findAndCount({
			relations: ["uploader"],
			order: { createdAt: "DESC" },
			skip,
			take: limit,
		});

		return {
			data: images,
			total,
			page,
			limit,
			totalPages: Math.ceil(total / limit),
		};
	}

	async update(id: number, data: Partial<Image>): Promise<Image> {
		await this.repository.update(id, data);
		const updated = await this.findById(id);
		if (!updated) {
			throw new Error(`Image with id ${id} not found`);
		}
		return updated;
	}

	async delete(id: number): Promise<void> {
		await this.repository.delete(id);
	}

	async findByFilename(filename: string): Promise<Image | null> {
		return await this.repository.findOne({
			where: { filename },
			relations: ["uploader"],
		});
	}

	async findByStatus(status: ImageStatus): Promise<Image[]> {
		return await this.repository.find({
			where: { status },
			relations: ["uploader"],
			order: { createdAt: "DESC" },
		});
	}

	async findByUploader(uploaderId: number): Promise<Image[]> {
		return await this.repository.find({
			where: { uploadedBy: uploaderId },
			relations: ["uploader"],
			order: { createdAt: "DESC" },
		});
	}

	async findByHash(hash: string): Promise<Image | null> {
		return await this.repository.findOne({
			where: { hash },
			relations: ["uploader"],
		});
	}

	async findOlderThan(date: Date): Promise<Image[]> {
		return await this.repository.find({
			where: { createdAt: LessThan(date) },
			relations: ["uploader"],
		});
	}

	async findTempImages(olderThanMinutes: number): Promise<Image[]> {
		const cutoffDate = new Date();
		cutoffDate.setMinutes(cutoffDate.getMinutes() - olderThanMinutes);

		return await this.repository.find({
			where: [
				{ status: ImageStatus.UPLOADING, createdAt: LessThan(cutoffDate) },
				{ status: ImageStatus.PROCESSING, createdAt: LessThan(cutoffDate) },
			],
			relations: ["uploader"],
		});
	}

	async updateStatus(id: number, status: ImageStatus): Promise<void> {
		await this.repository.update(id, { status });
	}

	async softDelete(id: number): Promise<void> {
		await this.repository.update(id, {
			status: ImageStatus.DELETED,
			deletedAt: new Date(),
		});
	}

	async hardDelete(id: number): Promise<void> {
		await this.repository.delete(id);
	}

	async countByStatus(status: ImageStatus): Promise<number> {
		return await this.repository.count({
			where: { status },
		});
	}

	async getTotalSizeByUser(uploaderId: number): Promise<number> {
		const result = await this.repository
			.createQueryBuilder("image")
			.select("SUM(image.size)", "totalSize")
			.where("image.uploadedBy = :uploaderId", { uploaderId })
			.andWhere("image.status = :status", { status: ImageStatus.ACTIVE })
			.getRawOne();

		return parseInt(result?.totalSize || "0");
	}

	async findWithPagination(
		page: number,
		limit: number,
		status?: ImageStatus
	): Promise<{
		images: Image[];
		total: number;
		totalPages: number;
		currentPage: number;
	}> {
		const skip = (page - 1) * limit;

		const queryBuilder = this.repository
			.createQueryBuilder("image")
			.leftJoinAndSelect("image.uploader", "uploader")
			.orderBy("image.createdAt", "DESC")
			.skip(skip)
			.take(limit);

		if (status) {
			queryBuilder.where("image.status = :status", { status });
		}

		const [images, total] = await queryBuilder.getManyAndCount();
		const totalPages = Math.ceil(total / limit);

		return {
			images,
			total,
			totalPages,
			currentPage: page,
		};
	}
}
