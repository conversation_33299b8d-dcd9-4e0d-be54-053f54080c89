import { Inject, Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { IReview, IReviewRepository } from "../../models/interfaces";

@Injectable()
export class GetReviewUseCase {
	constructor(
		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository
	) {}

	async execute(id: string, currentUserId?: string): Promise<IReview> {
		const review = await this.reviewRepository.findById(id);

		if (!review) {
			throw new ResourceNotFoundException("Avaliação", id);
		}

		// Se a review for privada, apenas o autor pode vê-la
		if (!review.isPublic && review.userId !== (currentUserId ? parseInt(currentUserId, 10) : undefined)) {
			throw new ResourceNotFoundException("Avaliação privada", `${id}:${currentUserId}`);
		}

		return review;
	}
}
