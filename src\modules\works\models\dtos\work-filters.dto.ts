import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsOptional, IsString, IsArray, IsNumber, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";
import { Transform, Type } from "class-transformer";
import { WorkType, WorkStatus } from "../enums";

export class WorkFiltersDto {
	@ApiPropertyOptional({
		description: "Tipo da obra",
		enum: WorkType,
		example: WorkType.MANHWA,
	})
	@IsOptional()
	@IsEnum(WorkType)
	type?: WorkType;

	@ApiPropertyOptional({
		description: "Status da obra",
		enum: WorkStatus,
		example: WorkStatus.ONGOING,
	})
	@IsOptional()
	@IsEnum(WorkStatus)
	status?: WorkStatus;

	@ApiPropertyOptional({
		description: "Autor da obra",
		example: "Chugong",
	})
	@IsOptional()
	@IsString()
	author?: string;

	@ApiPropertyOptional({
		description: "Artista da obra",
		example: "DUBU",
	})
	@IsOptional()
	@IsString()
	artist?: string;

	@ApiPropertyOptional({
		description: "Busca por título ou descrição",
		example: "Solo Leveling",
	})
	@IsOptional()
	@IsString()
	search?: string;

	@ApiPropertyOptional({
		description: "Tags para filtrar",
		example: ["action", "fantasy"],
		type: [String],
	})
	@IsOptional()
	@IsArray()
	@IsString({ each: true })
	@Transform(({ value }) => (Array.isArray(value) ? value : [value]))
	tags?: string[];

	@ApiPropertyOptional({
		description: "Avaliação mínima",
		example: 4.0,
		minimum: 0,
		maximum: 5,
	})
	@IsOptional()
	@IsNumber()
	@Min(0)
	@Max(5)
	@Type(() => Number)
	minRating?: number;

	@ApiPropertyOptional({
		description: "Avaliação máxima",
		example: 5.0,
		minimum: 0,
		maximum: 5,
	})
	@IsOptional()
	@IsNumber()
	@Min(0)
	@Max(5)
	@Type(() => Number)
	maxRating?: number;

	@ApiPropertyOptional({
		description: "Página",
		example: 1,
		minimum: 1,
		default: 1,
	})
	@IsOptional()
	@IsInt()
	@Min(1)
	@Type(() => Number)
	page?: number = 1;

	@ApiPropertyOptional({
		description: "Itens por página",
		example: 20,
		minimum: 1,
		maximum: 100,
		default: 20,
	})
	@IsOptional()
	@IsInt()
	@Min(1)
	@Max(100)
	@Type(() => Number)
	limit?: number = 20;

	@ApiPropertyOptional({
		description: "Campo para ordenação",
		enum: ["title", "releaseDate", "averageRating", "createdAt"],
		example: "averageRating",
		default: "createdAt",
	})
	@IsOptional()
	@IsEnum(["title", "releaseDate", "averageRating", "createdAt"])
	sortBy?: "title" | "releaseDate" | "averageRating" | "createdAt" = "createdAt";

	@ApiPropertyOptional({
		description: "Ordem da ordenação",
		enum: ["ASC", "DESC"],
		example: "DESC",
		default: "DESC",
	})
	@IsOptional()
	@IsEnum(["ASC", "DESC"])
	sortOrder?: "ASC" | "DESC" = "DESC";
}
