import { IReview, IReviewFilters } from "./IReview.interface";

export interface IReviewRepository {
	create(review: Partial<IReview>): Promise<IReview>;
	update(id: string, review: Partial<IReview>): Promise<IReview>;
	delete(id: string): Promise<void>;
	findById(id: string): Promise<IReview>;
	findAll(filters: IReviewFilters): Promise<IReview[]>;
	findCount(filters: IReviewFilters): Promise<number>;
	incrementLikes(id: string): Promise<void>;
	decrementLikes(id: string): Promise<void>;
	incrementDislikes(id: string): Promise<void>;
	decrementDislikes(id: string): Promise<void>;
	findByUserAndWork(userId: number, workId: string): Promise<IReview>;
	updateWorkAverageRating(workId: string): Promise<void>;
	countByUserId(userId: number): Promise<number>;
	countPublicByUserId(userId: number): Promise<number>;
	getUserAverageRating(userId: number): Promise<number | null>;
	getTotalLikesByUserId(userId: number): Promise<number>;
}
