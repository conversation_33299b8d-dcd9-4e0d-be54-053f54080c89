import { IBaseRepository } from "../../../../shared/interfaces/base-repository.interface";
import { Image } from "../entities/image.entity";
import { ImageStatus } from "../enums";

/**
 * Interface para repositório de imagens
 * Seguindo Interface Segregation Principle (ISP) do SOLID
 * Dependency Inversion Principle (DIP) - abstração para implementação
 */
export interface IImageRepository extends IBaseRepository<Image, number> {
	/**
	 * Busca imagem por nome do arquivo
	 */
	findByFilename(filename: string): Promise<Image | null>;

	/**
	 * Busca imagens por status
	 */
	findByStatus(status: ImageStatus): Promise<Image[]>;

	/**
	 * Busca imagens por usuário que fez upload
	 */
	findByUploader(uploaderId: number): Promise<Image[]>;

	/**
	 * Busca imagens por hash (para detectar duplicatas)
	 */
	findByHash(hash: string): Promise<Image | null>;

	/**
	 * Busca imagens criadas antes de uma data específica
	 */
	findOlderThan(date: Date): Promise<Image[]>;

	/**
	 * Busca imagens temporárias para limpeza
	 */
	findTempImages(olderThanMinutes: number): Promise<Image[]>;

	/**
	 * Atualiza status da imagem
	 */
	updateStatus(id: number, status: ImageStatus): Promise<void>;

	/**
	 * Marca imagem como deletada (soft delete)
	 */
	softDelete(id: number): Promise<void>;

	/**
	 * Remove imagem permanentemente
	 */
	hardDelete(id: number): Promise<void>;

	/**
	 * Conta total de imagens por status
	 */
	countByStatus(status: ImageStatus): Promise<number>;

	/**
	 * Calcula tamanho total de imagens por usuário
	 */
	getTotalSizeByUser(uploaderId: number): Promise<number>;

	/**
	 * Busca imagens com paginação
	 */
	findWithPagination(
		page: number,
		limit: number,
		status?: ImageStatus
	): Promise<{
		images: Image[];
		total: number;
		totalPages: number;
		currentPage: number;
	}>;
}
