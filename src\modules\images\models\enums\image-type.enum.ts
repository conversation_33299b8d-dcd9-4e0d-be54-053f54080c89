/**
 * Enum para tipos de imagem suportados
 * Seguindo princípios SOLID - Single Responsibility
 */
export enum ImageType {
	JPEG = "image/jpeg",
	JPG = "image/jpg",
	PNG = "image/png",
	WEBP = "image/webp",
	GIF = "image/gif",
}

/**
 * Extensões de arquivo correspondentes aos tipos MIME
 */
export enum ImageExtension {
	JPEG = ".jpeg",
	JPG = ".jpg",
	PNG = ".png",
	WEBP = ".webp",
	GIF = ".gif",
}

/**
 * Mapeamento entre tipos MIME e extensões
 */
export const MIME_TO_EXTENSION: Record<ImageType, ImageExtension> = {
	[ImageType.JPEG]: ImageExtension.JPEG,
	[ImageType.JPG]: ImageExtension.JPG,
	[ImageType.PNG]: ImageExtension.PNG,
	[ImageType.WEBP]: ImageExtension.WEBP,
	[ImageType.GIF]: ImageExtension.GIF,
};

/**
 * Array de tipos MIME suportados para validação
 */
export const SUPPORTED_IMAGE_TYPES = Object.values(ImageType);

/**
 * Array de extensões suportadas para validação
 */
export const SUPPORTED_IMAGE_EXTENSIONS = Object.values(ImageExtension);
