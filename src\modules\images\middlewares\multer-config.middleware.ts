import { Injectable } from '@nestjs/common';
import { MulterModuleOptions, MulterOptionsFactory } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { Request } from 'express';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Configuração do Multer para upload de imagens
 * Seguindo princípios SOLID - Single Responsibility
 */
@Injectable()
export class MulterConfigService implements MulterOptionsFactory {
	createMulterOptions(): MulterModuleOptions {
		return {
			storage: diskStorage({
				destination: this.getDestination,
				filename: this.generateFilename
			}),
			limits: {
				fileSize: 10 * 1024 * 1024, // 10MB
				files: 1 // Apenas um arquivo por vez
			},
			fileFilter: this.fileFilter
		};
	}

	private getDestination(req: Request, file: Express.Multer.File, callback: Function): void {
		// Usar pasta temp por padrão
		const uploadPath = path.join(process.cwd(), 'temp');
		
		// Criar pasta se não existir
		if (!fs.existsSync(uploadPath)) {
			fs.mkdirSync(uploadPath, { recursive: true });
		}

		callback(null, uploadPath);
	}

	private generateFilename(req: Request, file: Express.Multer.File, callback: Function): void {
		// Gerar nome único baseado em timestamp e hash
		const timestamp = Date.now();
		const randomString = Math.random().toString(36).substring(2, 8);
		const extension = path.extname(file.originalname).toLowerCase();
		
		const filename = `img_${timestamp}_${randomString}${extension}`;
		callback(null, filename);
	}

	private fileFilter(req: Request, file: Express.Multer.File, callback: Function): void {
		// Validação básica de tipo MIME
		const allowedMimeTypes = [
			'image/jpeg',
			'image/jpg', 
			'image/png',
			'image/webp',
			'image/gif'
		];

		if (allowedMimeTypes.includes(file.mimetype)) {
			callback(null, true);
		} else {
			callback(new Error(`Tipo de arquivo não suportado: ${file.mimetype}`), false);
		}
	}
}

/**
 * Configuração específica para diferentes tipos de upload
 */
export class MulterConfigFactory {
	/**
	 * Configuração para upload de avatares
	 */
	static forAvatars(): MulterModuleOptions {
		return {
			storage: diskStorage({
				destination: (req, file, cb) => {
					const uploadPath = path.join(process.cwd(), 'temp', 'avatars');
					if (!fs.existsSync(uploadPath)) {
						fs.mkdirSync(uploadPath, { recursive: true });
					}
					cb(null, uploadPath);
				},
				filename: (req, file, cb) => {
					const timestamp = Date.now();
					const randomString = Math.random().toString(36).substring(2, 8);
					const extension = path.extname(file.originalname).toLowerCase();
					cb(null, `avatar_${timestamp}_${randomString}${extension}`);
				}
			}),
			limits: {
				fileSize: 2 * 1024 * 1024, // 2MB para avatares
				files: 1
			},
			fileFilter: (req, file, cb) => {
				const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
				if (allowedMimeTypes.includes(file.mimetype)) {
					cb(null, true);
				} else {
					cb(new Error('Apenas imagens JPEG, PNG e WebP são permitidas para avatares'), false);
				}
			}
		};
	}

	/**
	 * Configuração para upload de conteúdo geral
	 */
	static forContent(): MulterModuleOptions {
		return {
			storage: diskStorage({
				destination: (req, file, cb) => {
					const uploadPath = path.join(process.cwd(), 'temp', 'content');
					if (!fs.existsSync(uploadPath)) {
						fs.mkdirSync(uploadPath, { recursive: true });
					}
					cb(null, uploadPath);
				},
				filename: (req, file, cb) => {
					const timestamp = Date.now();
					const randomString = Math.random().toString(36).substring(2, 8);
					const extension = path.extname(file.originalname).toLowerCase();
					cb(null, `content_${timestamp}_${randomString}${extension}`);
				}
			}),
			limits: {
				fileSize: 10 * 1024 * 1024, // 10MB para conteúdo
				files: 1
			},
			fileFilter: (req, file, cb) => {
				const allowedMimeTypes = [
					'image/jpeg',
					'image/jpg',
					'image/png', 
					'image/webp',
					'image/gif'
				];
				if (allowedMimeTypes.includes(file.mimetype)) {
					cb(null, true);
				} else {
					cb(new Error('Tipo de arquivo não suportado'), false);
				}
			}
		};
	}

	/**
	 * Configuração para upload múltiplo
	 */
	static forMultiple(maxFiles: number = 5): MulterModuleOptions {
		return {
			storage: diskStorage({
				destination: (req, file, cb) => {
					const uploadPath = path.join(process.cwd(), 'temp', 'multiple');
					if (!fs.existsSync(uploadPath)) {
						fs.mkdirSync(uploadPath, { recursive: true });
					}
					cb(null, uploadPath);
				},
				filename: (req, file, cb) => {
					const timestamp = Date.now();
					const randomString = Math.random().toString(36).substring(2, 8);
					const extension = path.extname(file.originalname).toLowerCase();
					cb(null, `multi_${timestamp}_${randomString}${extension}`);
				}
			}),
			limits: {
				fileSize: 5 * 1024 * 1024, // 5MB por arquivo
				files: maxFiles
			},
			fileFilter: (req, file, cb) => {
				const allowedMimeTypes = [
					'image/jpeg',
					'image/jpg',
					'image/png',
					'image/webp'
				];
				if (allowedMimeTypes.includes(file.mimetype)) {
					cb(null, true);
				} else {
					cb(new Error('Tipo de arquivo não suportado para upload múltiplo'), false);
				}
			}
		};
	}
}
