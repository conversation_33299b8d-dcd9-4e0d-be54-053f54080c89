import { Inject, Injectable } from "@nestjs/common";
import { DuplicateResourceException, ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { CreateReviewReactionDto } from "../../models/dtos";
import { IReviewReaction, IReviewReactionRepository, IReviewRepository } from "../../models/interfaces";

@Injectable()
export class CreateReviewReactionUseCase {
	constructor(
		@Inject("IReviewReactionRepository")
		private readonly reactionRepository: IReviewReactionRepository,

		@Inject("IReviewRepository")
		private readonly reviewRepository: IReviewRepository
	) {}

	async execute(userId: string, createReactionDto: CreateReviewReactionDto): Promise<IReviewReaction> {
		// Verificar se a review existe
		const review = await this.reviewRepository.findById(createReactionDto.reviewId);
		if (!review) {
			throw new ResourceNotFoundException("Avaliação", createReactionDto.reviewId);
		}

		// Verificar se o usuário já reagiu a esta review
		const existingReaction = await this.reactionRepository.findByReviewAndUser(createReactionDto.reviewId, userId);

		if (existingReaction) {
			// Se for o mesmo tipo de reação, retorna erro
			if (existingReaction.isLike === createReactionDto.isLike) {
				throw new DuplicateResourceException("Reação", "tipo", createReactionDto.isLike ? "like" : "dislike");
			}

			// Se for uma reação diferente, remove a anterior e ajusta os contadores
			await this.reactionRepository.delete(existingReaction.id);

			if (existingReaction.isLike) {
				await this.reviewRepository.decrementLikes(createReactionDto.reviewId);
			} else {
				await this.reviewRepository.decrementDislikes(createReactionDto.reviewId);
			}
		}

		// Criar a reação
		const reaction = {
			userId: Number(userId),
			reviewId: createReactionDto.reviewId,
			isLike: createReactionDto.isLike,
		};

		// Atualizar os contadores na review
		if (createReactionDto.isLike) {
			await this.reviewRepository.incrementLikes(createReactionDto.reviewId);
		} else {
			await this.reviewRepository.incrementDislikes(createReactionDto.reviewId);
		}

		return await this.reactionRepository.create(reaction);
	}
}
