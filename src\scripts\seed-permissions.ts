import { NestFactory } from "@nestjs/core";
import { AppModule } from "../app.module";
import { SeedPermissionsUseCase } from "../modules/permissions/use-cases";

/**
 * Script para executar o seed do sistema de permissões
 * Uso: npm run seed:permissions
 */
async function seedPermissions() {
	console.log("🌱 Iniciando seed do sistema de permissões...");

	try {
		// Criar a aplicação NestJS
		const app = await NestFactory.createApplicationContext(AppModule);

		// Obter o use case de seed
		const seedPermissionsUseCase = app.get(SeedPermissionsUseCase);

		// Executar o seed
		await seedPermissionsUseCase.execute();

		console.log("✅ Seed do sistema de permissões concluído com sucesso!");

		// Fechar a aplicação
		await app.close();
	} catch (error) {
		console.error("❌ Erro durante o seed:", error.message);
		console.error(error.stack);
		process.exit(1);
	}
}

// Executar o script se for chamado diretamente
if (require.main === module) {
	seedPermissions();
}

export { seedPermissions };
