import { Injectable } from "@nestjs/common";
import { ResourceNotFoundException } from "src/shared/exceptions/business.exceptions";
import { WorkRepository } from "../../repositories";

@Injectable()
export class DeleteWorkUseCase {
	constructor(private readonly workRepository: WorkRepository) {}

	async execute(id: string): Promise<void> {
		// Verificar se a obra existe
		const existingWork = await this.workRepository.findById(id);
		if (!existingWork) {
			throw new ResourceNotFoundException("Obra", id);
		}

		await this.workRepository.delete(id);
	}
}
