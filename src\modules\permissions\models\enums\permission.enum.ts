/**
 * Enum que define todas as permissões granulares do sistema
 * Seguindo o padrão: RESOURCE_ACTION
 *
 * @enum {string}
 */
export enum Permission {
	// ===== USUÁRIOS =====
	/**
	 * Permissões relacionadas ao gerenciamento de usuários
	 */
	USERS_CREATE = "users:create",
	USERS_READ = "users:read",
	USERS_UPDATE = "users:update",
	USERS_DELETE = "users:delete",
	USERS_LIST = "users:list",
	USERS_MANAGE_ROLES = "users:manage_roles",

	// ===== OBRAS =====
	/**
	 * Permissões relacionadas ao gerenciamento de obras (manhwa/manga/manhua)
	 */
	WORKS_CREATE = "works:create",
	WORKS_READ = "works:read",
	WORKS_UPDATE = "works:update",
	WORKS_DELETE = "works:delete",
	WORKS_LIST = "works:list",
	WORKS_MODERATE = "works:moderate",

	// ===== LEITURA =====
	/**
	 * Permissões relacionadas ao controle de leitura
	 */
	READING_CREATE = "reading:create",
	READING_READ = "reading:read",
	READING_UPDATE = "reading:update",
	READING_DELETE = "reading:delete",
	READING_LIST = "reading:list",

	// ===== LISTAS =====
	/**
	 * Permissões relacionadas às listas personalizadas
	 */
	LISTS_CREATE = "lists:create",
	LISTS_READ = "lists:read",
	LISTS_UPDATE = "lists:update",
	LISTS_DELETE = "lists:delete",
	LISTS_LIST = "lists:list",
	LISTS_MODERATE = "lists:moderate",

	// ===== RANKINGS =====
	/**
	 * Permissões relacionadas aos rankings
	 */
	RANKINGS_CREATE = "rankings:create",
	RANKINGS_READ = "rankings:read",
	RANKINGS_UPDATE = "rankings:update",
	RANKINGS_DELETE = "rankings:delete",
	RANKINGS_LIST = "rankings:list",
	RANKINGS_MODERATE = "rankings:moderate",

	// ===== REVIEWS =====
	/**
	 * Permissões relacionadas às avaliações e comentários
	 */
	REVIEWS_CREATE = "reviews:create",
	REVIEWS_READ = "reviews:read",
	REVIEWS_UPDATE = "reviews:update",
	REVIEWS_DELETE = "reviews:delete",
	REVIEWS_LIST = "reviews:list",
	REVIEWS_MODERATE = "reviews:moderate",

	// ===== TAGS =====
	/**
	 * Permissões relacionadas ao sistema de tags
	 */
	TAGS_CREATE = "tags:create",
	TAGS_READ = "tags:read",
	TAGS_UPDATE = "tags:update",
	TAGS_DELETE = "tags:delete",
	TAGS_LIST = "tags:list",
	TAGS_MODERATE = "tags:moderate",

	// ===== ADMINISTRAÇÃO =====
	/**
	 * Permissões administrativas do sistema
	 */
	ADMIN_DASHBOARD = "admin:dashboard",
	ADMIN_SYSTEM_CONFIG = "admin:system_config",
	ADMIN_LOGS = "admin:logs",
	ADMIN_METRICS = "admin:metrics",
	ADMIN_BACKUP = "admin:backup",

	// ===== MODERAÇÃO =====
	/**
	 * Permissões de moderação geral
	 */
	MODERATE_CONTENT = "moderate:content",
	MODERATE_USERS = "moderate:users",
	MODERATE_REPORTS = "moderate:reports",

	// ===== PERMISSÕES =====
	/**
	 * Permissões relacionadas ao gerenciamento de roles e permissões
	 */
	PERMISSIONS_CREATE = "permissions:create",
	PERMISSIONS_READ = "permissions:read",
	PERMISSIONS_UPDATE = "permissions:update",
	PERMISSIONS_DELETE = "permissions:delete",
	PERMISSIONS_ASSIGN = "permissions:assign",
}
