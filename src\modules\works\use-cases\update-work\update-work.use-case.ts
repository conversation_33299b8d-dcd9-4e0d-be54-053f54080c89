import { Injectable, Inject } from "@nestjs/common";
import {
	DuplicateResourceException,
	ResourceNotFoundException,
	ValidationException,
	UnauthorizedOperationException,
} from "src/shared/exceptions/business.exceptions";
import { IUpdateWorkRequest, IWork, IWorkRepository } from "../../models/interfaces";
import { IImageRepository } from "../../../images/models/interfaces";
import { UserRole } from "../../../user/models/enums";

@Injectable()
export class UpdateWorkUseCase {
	constructor(
		@Inject("IWorkRepository")
		private readonly workRepository: IWorkRepository,
		@Inject("IImageRepository")
		private readonly imageRepository: IImageRepository
	) {}

	async execute(id: string, data: IUpdateWorkRequest, userId?: number, userRole?: UserRole): Promise<IWork> {
		// Verificar se a obra existe
		const existingWork = await this.workRepository.findById(id);
		if (!existingWork) {
			throw new ResourceNotFoundException("Obra", id);
		}

		// Se está tentando alterar o título, verificar se não existe outra obra com o mesmo título
		if (data.title && data.title !== existingWork.title) {
			const workWithSameTitle = await this.workRepository.findByTitle(data.title);
			if (workWithSameTitle) {
				throw new DuplicateResourceException("Obra", "title", data.title);
			}
		}

		// Validar nova imagem de capa se fornecida
		if (data.coverImageId) {
			await this.validateImageAccess(data.coverImageId, userId, userRole);
		}

		// Validar novas imagens da galeria se fornecidas
		if (data.galleryImageIds && data.galleryImageIds.length > 0) {
			await Promise.all(data.galleryImageIds.map(imageId => this.validateImageAccess(imageId, userId, userRole)));
		}

		// Atualizar a obra
		const updatedWork = await this.workRepository.update(id, data);

		// Atualizar associações de imagens se necessário
		if (data.coverImageId !== undefined) {
			if (data.coverImageId === null) {
				await this.workRepository.removeCoverImage(id);
			} else {
				await this.workRepository.associateCoverImage(id, data.coverImageId);
			}
		}

		if (data.galleryImageIds !== undefined) {
			// Remover todas as imagens da galeria e adicionar as novas
			await this.workRepository.removeAllGalleryImages(id);
			if (data.galleryImageIds.length > 0) {
				await this.workRepository.associateGalleryImages(id, data.galleryImageIds);
			}
		}

		// Retornar a obra atualizada com as imagens
		return (await this.workRepository.findByIdWithImages(id)) || updatedWork;
	}

	private async validateImageAccess(imageId: number, userId?: number, userRole?: UserRole): Promise<void> {
		const image = await this.imageRepository.findById(imageId);

		if (!image) {
			throw new ValidationException({
				imageId: [`Imagem com ID ${imageId} não encontrada`],
			});
		}

		if (!image.isAvailable()) {
			throw new ValidationException({
				imageId: [`Imagem com ID ${imageId} não está disponível`],
			});
		}

		// Validar propriedade (apenas proprietário ou admin pode usar)
		const isAdmin = userRole === UserRole.ADMIN;
		const isOwner = userId && image.uploadedBy === userId;

		if (!isAdmin && !isOwner) {
			throw new UnauthorizedOperationException(`Você não tem permissão para usar a imagem ${imageId}`);
		}
	}
}
