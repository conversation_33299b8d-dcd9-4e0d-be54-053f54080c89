import { Injectable, Inject, NotFoundException } from "@nestjs/common";
import { BaseUseCase } from "../../../../shared/use-cases/base.use-case";
import { IUserRoleRepository } from "../../models/interfaces";
import { IUserRepository } from "../../../user/models/interfaces/user-repository.interface";
import { Permission } from "../../models/enums";

/**
 * Use case para buscar todas as permissões de um usuário
 */
@Injectable()
export class GetUserPermissionsUseCase extends BaseUseCase<number, Permission[]> {
	constructor(
		@Inject("IUserRoleRepository")
		private readonly userRoleRepository: IUserRoleRepository,
		@Inject("IUserRepository")
		private readonly userRepository: IUserRepository
	) {
		super("GetUserPermissionsUseCase");
	}

	/**
	 * Executa a busca das permissões do usuário
	 *
	 * @param userId - ID do usuário
	 * @returns Lista de permissões do usuário
	 */
	async execute(userId: number): Promise<Permission[]> {
		this.logger.log(`Buscando permissões do usuário ${userId}`);

		try {
			// Verifica se o usuário existe
			const user = await this.userRepository.findById(userId);
			if (!user) {
				throw new NotFoundException(`Usuário com ID ${userId} não encontrado`);
			}

			// Busca todas as permissões do usuário através de seus roles
			const permissions = await this.userRoleRepository.getUserPermissions(userId);

			this.logger.log(`${permissions.length} permissões encontradas para o usuário ${user.username}`);

			return permissions;
		} catch (error) {
			this.logger.error(`Erro ao buscar permissões do usuário: ${error.message}`, error.stack);
			throw error;
		}
	}
}
