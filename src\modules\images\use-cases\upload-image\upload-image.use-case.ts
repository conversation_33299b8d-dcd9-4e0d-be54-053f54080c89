import { Inject, Injectable } from "@nestjs/common";
import { BaseUseCase } from "../../../../shared/use-cases/base.use-case";
import { IImageRepository } from "../../models/interfaces/image-repository.interface";
import { UploadImageDto, ImageResponseDto } from "../../models/dtos";
import { Image } from "../../models/entities/image.entity";
import { ImageStatus, ImageType, MIME_TO_EXTENSION } from "../../models/enums";
import { ValidationException, BusinessException } from "../../../../shared/exceptions/business.exceptions";
import * as fs from "fs-extra";
import * as path from "path";
import * as crypto from "crypto";
import * as sharp from "sharp";

/**
 * Interface para dados de entrada do use case
 */
export interface IUploadImageInput {
	file: Express.Multer.File;
	uploadData: UploadImageDto;
	uploaderId?: number;
}

/**
 * Use Case para upload de imagem
 * Seguindo princípios SOLID - Single Responsibility
 * Contém toda a lógica de validação, processamento e manipulação de arquivos
 */
@Injectable()
export class UploadImageUseCase extends BaseUseCase<IUploadImageInput, ImageResponseDto> {
	private readonly baseUploadPath: string;
	private readonly tempPath: string;
	private readonly supportedMimeTypes = [ImageType.JPEG, ImageType.JPG, ImageType.PNG, ImageType.WEBP, ImageType.GIF];

	constructor(
		@Inject("IImageRepository")
		private readonly imageRepository: IImageRepository
	) {
		super("UploadImageUseCase");
		this.baseUploadPath = path.join(process.cwd(), "uploads", "images");
		this.tempPath = path.join(process.cwd(), "uploads", "temp");
	}

	async execute(input: IUploadImageInput): Promise<ImageResponseDto> {
		return this.executeWithLogging("UploadImage", input, async () => {
			const { file, uploadData, uploaderId } = input;

			// 1. Validar arquivo
			await this.validateFile(file, uploadData);

			// 2. Gerar nome único para o arquivo
			const filename = this.generateUniqueFilename(file.originalname, file.mimetype as ImageType);

			// 3. Salvar arquivo temporariamente
			const tempPath = await this.saveFile(file, `temp/${filename}`);

			// 4. Criar registro inicial no banco
			const imageData: Partial<Image> = {
				filename,
				originalName: file.originalname,
				path: tempPath,
				mimeType: file.mimetype as ImageType,
				size: file.size,
				status: ImageStatus.UPLOADING,
				uploadedBy: uploaderId,
			};

			const image = await this.imageRepository.create(imageData);

			try {
				// 5. Processar imagem
				await this.processImage(image, tempPath, uploadData);

				// 6. Mover para pasta permanente
				const permanentPath = `images/${new Date().getFullYear()}/${String(new Date().getMonth() + 1).padStart(2, "0")}/${filename}`;
				await this.moveFromTemp(tempPath, permanentPath);

				// 7. Atualizar registro no banco
				const updatedImage = await this.imageRepository.update(image.id, {
					path: permanentPath,
					status: ImageStatus.ACTIVE,
					url: `/api/images/${image.id}`,
				});

				return this.mapToResponseDto(updatedImage);
			} catch (error) {
				// Em caso de erro, marcar como falha e limpar arquivo temporário
				await this.imageRepository.updateStatus(image.id, ImageStatus.FAILED);
				await this.deleteFile(tempPath).catch(() => {
					// Ignorar erro de limpeza
				});
				throw error;
			}
		});
	}

	private async validateFile(file: Express.Multer.File, uploadData: UploadImageDto): Promise<void> {
		// Validar se é uma imagem válida
		const isValidImage = await this.validateImageFile(file);
		if (!isValidImage) {
			throw new ValidationException({ file: ["Arquivo não é uma imagem válida"] });
		}

		// Validar tipo MIME
		if (!this.validateMimeType(file.mimetype)) {
			throw new ValidationException({ mimetype: [`Tipo de arquivo não suportado: ${file.mimetype}`] });
		}

		// Validar tamanho
		const maxSizeInMB = 10; // Configurável
		if (!this.validateFileSize(file.size, maxSizeInMB)) {
			throw new ValidationException({ size: [`Arquivo muito grande. Máximo permitido: ${maxSizeInMB}MB`] });
		}
	}

	private async processImage(image: Image, filePath: string, uploadData: UploadImageDto): Promise<void> {
		// Atualizar status para processamento
		await this.imageRepository.updateStatus(image.id, ImageStatus.PROCESSING);

		// Obter metadados da imagem
		const metadata = await this.getImageMetadata(filePath);

		// Validar dimensões
		if (uploadData.maxWidth && uploadData.maxHeight) {
			if (!this.validateDimensions(metadata.width, metadata.height, uploadData.maxWidth, uploadData.maxHeight)) {
				throw new ValidationException({ dimensions: [`Dimensões da imagem excedem o limite: ${uploadData.maxWidth}x${uploadData.maxHeight}`] });
			}
		}

		// Gerar hash para detecção de duplicatas
		const hash = await this.generateImageHash(filePath);

		// Verificar se já existe imagem com mesmo hash
		const existingImage = await this.imageRepository.findByHash(hash);
		if (existingImage && existingImage.id !== image.id) {
			throw new BusinessException("Imagem duplicada já existe no sistema");
		}

		// Otimizar imagem se solicitado
		if (uploadData.optimize) {
			await this.optimizeImage(filePath, filePath, uploadData.quality || 85);
		}

		// Redimensionar se necessário
		if (uploadData.maxWidth && uploadData.maxHeight) {
			await this.resizeImage(filePath, filePath, uploadData.maxWidth, uploadData.maxHeight);
		}

		// Atualizar metadados no banco
		await this.imageRepository.update(image.id, {
			width: metadata.width,
			height: metadata.height,
			hash,
			metadata: JSON.stringify({
				originalFormat: metadata.format,
				processed: true,
				optimized: uploadData.optimize,
				resized: !!(uploadData.maxWidth && uploadData.maxHeight),
			}),
		});
	}

	private mapToResponseDto(image: Image): ImageResponseDto {
		return {
			id: image.id,
			filename: image.filename,
			originalName: image.originalName,
			path: image.path,
			url: image.url || "",
			mimeType: image.mimeType,
			size: image.size,
			formattedSize: image.getFormattedSize(),
			width: image.width,
			height: image.height,
			dimensions: image.getDimensions(),
			status: image.status,
			hash: image.hash,
			metadata: image.metadata,
			uploadedBy: image.uploadedBy,
			createdAt: image.createdAt,
			updatedAt: image.updatedAt,
			deletedAt: image.deletedAt,
		};
	}

	// ========== MÉTODOS UTILITÁRIOS ==========

	/**
	 * Valida se o arquivo é uma imagem válida usando Sharp
	 */
	private async validateImageFile(file: Express.Multer.File): Promise<boolean> {
		try {
			const metadata = await sharp(file.buffer || file.path).metadata();
			return !!(metadata.width && metadata.height && metadata.format);
		} catch {
			return false;
		}
	}

	/**
	 * Valida tipo MIME da imagem
	 */
	private validateMimeType(mimeType: string): boolean {
		return this.supportedMimeTypes.includes(mimeType as ImageType);
	}

	/**
	 * Valida tamanho do arquivo
	 */
	private validateFileSize(size: number, maxSizeInMB: number = 10): boolean {
		const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
		return size <= maxSizeInBytes && size > 0;
	}

	/**
	 * Valida dimensões da imagem
	 */
	private validateDimensions(width: number, height: number, maxWidth?: number, maxHeight?: number): boolean {
		if (!maxWidth || !maxHeight) return true;
		return width > 0 && height > 0 && width <= maxWidth && height <= maxHeight;
	}

	/**
	 * Obtém metadados da imagem usando Sharp
	 */
	private async getImageMetadata(filePath: string): Promise<{
		width: number;
		height: number;
		format: string;
		size: number;
	}> {
		const metadata = await sharp(filePath).metadata();
		const stats = await fs.stat(filePath);

		return {
			width: metadata.width || 0,
			height: metadata.height || 0,
			format: metadata.format || "unknown",
			size: stats.size,
		};
	}

	/**
	 * Gera hash da imagem para detectar duplicatas
	 */
	private async generateImageHash(filePath: string): Promise<string> {
		const buffer = await fs.readFile(filePath);
		return crypto.createHash("sha256").update(buffer).digest("hex");
	}

	/**
	 * Gera nome único para arquivo
	 */
	private generateUniqueFilename(originalName: string, mimeType: ImageType): string {
		const timestamp = Date.now();
		const random = Math.random().toString(36).substring(2, 8);
		const extension = MIME_TO_EXTENSION[mimeType] || ".jpg";
		const sanitizedName = this.sanitizeFilename(path.parse(originalName).name);

		return `${sanitizedName}_${timestamp}_${random}${extension}`;
	}

	/**
	 * Sanitiza nome do arquivo
	 */
	private sanitizeFilename(filename: string): string {
		return filename
			.toLowerCase()
			.replace(/[^a-z0-9]/g, "_")
			.replace(/_+/g, "_")
			.replace(/^_|_$/g, "")
			.substring(0, 50);
	}

	/**
	 * Salva arquivo no sistema de arquivos
	 */
	private async saveFile(file: Express.Multer.File, destinationPath: string): Promise<string> {
		const fullPath = path.join(this.tempPath, destinationPath);
		const directory = path.dirname(fullPath);

		// Criar diretório se não existir
		await fs.ensureDir(directory);

		// Salvar arquivo
		if (file.buffer) {
			await fs.writeFile(fullPath, file.buffer);
		} else if (file.path) {
			await fs.copy(file.path, fullPath);
		} else {
			throw new Error("Arquivo não possui buffer nem path");
		}

		return fullPath;
	}

	/**
	 * Move arquivo de temp para permanente
	 */
	private async moveFromTemp(tempPath: string, permanentPath: string): Promise<void> {
		const fullPermanentPath = path.join(this.baseUploadPath, permanentPath);
		const directory = path.dirname(fullPermanentPath);

		// Criar diretório se não existir
		await fs.ensureDir(directory);

		// Mover arquivo
		await fs.move(tempPath, fullPermanentPath);
	}

	/**
	 * Remove arquivo do sistema
	 */
	private async deleteFile(filePath: string): Promise<void> {
		try {
			await fs.remove(filePath);
		} catch (error) {
			// Ignorar erro se arquivo não existir
			if ((error as any).code !== "ENOENT") {
				throw error;
			}
		}
	}

	/**
	 * Otimiza imagem para web
	 */
	private async optimizeImage(inputPath: string, outputPath: string, quality: number = 85): Promise<void> {
		await sharp(inputPath).jpeg({ quality, progressive: true }).png({ compressionLevel: 9 }).webp({ quality }).toFile(outputPath);
	}

	/**
	 * Redimensiona imagem se necessário
	 */
	private async resizeImage(inputPath: string, outputPath: string, maxWidth: number, maxHeight: number): Promise<void> {
		await sharp(inputPath)
			.resize(maxWidth, maxHeight, {
				fit: "inside",
				withoutEnlargement: true,
			})
			.toFile(outputPath);
	}
}
