import { ApiProperty } from "@nestjs/swagger";
import { RoleResponseDto } from "./role-response.dto";

/**
 * DTO de resposta para uma relação usuário-role
 */
export class UserRoleResponseDto {
	@ApiProperty({
		description: "ID único da relação usuário-role",
		example: "550e8400-e29b-41d4-a716-************",
		format: "uuid",
	})
	id: string;

	@ApiProperty({
		description: "ID do usuário",
		example: 123,
	})
	userId: number;

	@ApiProperty({
		description: "ID do role",
		example: "550e8400-e29b-41d4-a716-************",
		format: "uuid",
	})
	roleId: string;

	@ApiProperty({
		description: "Se a atribuição está ativa",
		example: true,
	})
	isActive: boolean;

	@ApiProperty({
		description: "Data de expiração do role (se aplicável)",
		example: "2024-12-31T23:59:59.000Z",
		format: "date-time",
		nullable: true,
	})
	expiresAt?: Date;

	@ApiProperty({
		description: "ID do usuário que atribuiu o role",
		example: 456,
		nullable: true,
	})
	assignedBy?: number;

	@ApiProperty({
		description: "Data de criação da atribuição",
		example: "2024-06-10T08:00:00.000Z",
		format: "date-time",
	})
	createdAt: Date;

	@ApiProperty({
		description: "Data da última atualização",
		example: "2024-06-12T10:30:00.000Z",
		format: "date-time",
	})
	updatedAt: Date;
}

/**
 * DTO de resposta para uma relação usuário-role com detalhes do role
 */
export class UserRoleWithDetailsResponseDto extends UserRoleResponseDto {
	@ApiProperty({
		description: "Detalhes do role",
		type: RoleResponseDto,
	})
	role: RoleResponseDto;
}
