import { Inject, Injectable } from "@nestjs/common";
import { BaseUseCase } from "../../../../shared/use-cases/base.use-case";
import { IImageRepository } from "../../models/interfaces/image-repository.interface";
import { ImageStatus } from "../../models/enums";
import { ResourceNotFoundException, BusinessException } from "../../../../shared/exceptions/business.exceptions";
import * as fs from "fs-extra";
import * as path from "path";

/**
 * Interface para entrada do use case de deleção
 */
export interface IDeleteImageInput {
	imageId: number;
	userId?: number;
	hardDelete?: boolean;
}

/**
 * Interface para resposta de deleção
 */
export interface IDeleteImageResponse {
	success: boolean;
	message: string;
	deletedId: string;
	deletedAt: string;
	hardDeleted: boolean;
}

/**
 * Use Case para deletar imagem
 * Seguindo princípios SOLID - Single Responsibility
 */
@Injectable()
export class DeleteImageUseCase extends BaseUseCase<IDeleteImageInput, IDeleteImageResponse> {
	private readonly baseUploadPath: string;

	constructor(
		@Inject("IImageRepository")
		private readonly imageRepository: IImageRepository
	) {
		super("DeleteImageUseCase");
		this.baseUploadPath = path.join(process.cwd(), "uploads", "images");
	}

	async execute(input: IDeleteImageInput): Promise<IDeleteImageResponse> {
		return this.executeWithLogging("DeleteImage", input, async () => {
			const { imageId, userId, hardDelete = false } = input;

			// Buscar imagem
			const image = await this.imageRepository.findById(imageId);

			if (!image) {
				throw new ResourceNotFoundException("Imagem", imageId);
			}

			// Verificar permissões se userId fornecido
			if (userId && image.uploadedBy && image.uploadedBy !== userId) {
				throw new BusinessException("Você não tem permissão para deletar esta imagem");
			}

			// Verificar se imagem já foi deletada
			if (image.status === ImageStatus.DELETED && image.deletedAt) {
				throw new BusinessException("Imagem já foi deletada anteriormente");
			}

			const deletedAt = new Date().toISOString();

			if (hardDelete) {
				// Deleção permanente
				await this.performHardDelete(image);

				return {
					success: true,
					message: "Imagem deletada permanentemente com sucesso",
					deletedId: imageId.toString(),
					deletedAt,
					hardDeleted: true,
				};
			} else {
				// Deleção lógica (soft delete)
				await this.performSoftDelete(imageId);

				return {
					success: true,
					message: "Imagem marcada como deletada com sucesso",
					deletedId: imageId.toString(),
					deletedAt,
					hardDeleted: false,
				};
			}
		});
	}

	private async performSoftDelete(imageId: number): Promise<void> {
		// Marcar como deletada no banco
		await this.imageRepository.softDelete(imageId);
	}

	private async performHardDelete(image: any): Promise<void> {
		try {
			// Remover arquivo físico
			const fullPath = this.getFullPath(image.path);
			const fileExists = await this.fileExists(fullPath);

			if (fileExists) {
				await this.deleteFile(fullPath);
			}

			// Remover registro do banco
			await this.imageRepository.hardDelete(image.id);
		} catch (error) {
			// Se falhar ao deletar arquivo, pelo menos marcar como deletada
			await this.imageRepository.softDelete(image.id);
			throw new BusinessException("Erro ao deletar arquivo físico. Imagem marcada como deletada no banco de dados.");
		}
	}

	// ========== MÉTODOS UTILITÁRIOS ==========

	/**
	 * Obtém caminho completo do arquivo
	 */
	private getFullPath(relativePath: string): string {
		return path.join(this.baseUploadPath, relativePath);
	}

	/**
	 * Verifica se arquivo existe
	 */
	private async fileExists(filePath: string): Promise<boolean> {
		try {
			await fs.access(filePath);
			return true;
		} catch {
			return false;
		}
	}

	/**
	 * Remove arquivo do sistema
	 */
	private async deleteFile(filePath: string): Promise<void> {
		try {
			await fs.remove(filePath);
		} catch (error) {
			// Ignorar erro se arquivo não existir
			if ((error as any).code !== "ENOENT") {
				throw error;
			}
		}
	}
}

/**
 * Use Case para restaurar imagem deletada (soft delete)
 */
@Injectable()
export class RestoreImageUseCase extends BaseUseCase<number, IDeleteImageResponse> {
	private readonly baseUploadPath: string;

	constructor(
		@Inject("IImageRepository")
		private readonly imageRepository: IImageRepository
	) {
		super("RestoreImageUseCase");
		this.baseUploadPath = path.join(process.cwd(), "uploads", "images");
	}

	async execute(imageId: number): Promise<IDeleteImageResponse> {
		return this.executeWithLogging("RestoreImage", imageId, async () => {
			// Buscar imagem
			const image = await this.imageRepository.findById(imageId);

			if (!image) {
				throw new ResourceNotFoundException("Imagem", imageId);
			}

			// Verificar se imagem está deletada
			if (image.status !== ImageStatus.DELETED) {
				throw new BusinessException("Imagem não está deletada");
			}

			// Verificar se arquivo ainda existe
			const fullPath = this.getFullPath(image.path);
			const fileExists = await this.fileExists(fullPath);

			if (!fileExists) {
				throw new BusinessException("Arquivo físico não encontrado. Não é possível restaurar.");
			}

			// Restaurar imagem
			await this.imageRepository.update(imageId, {
				status: ImageStatus.ACTIVE,
				deletedAt: null,
			});

			return {
				success: true,
				message: "Imagem restaurada com sucesso",
				deletedId: imageId.toString(),
				deletedAt: new Date().toISOString(),
				hardDeleted: false,
			};
		});
	}

	// ========== MÉTODOS UTILITÁRIOS ==========

	/**
	 * Obtém caminho completo do arquivo
	 */
	private getFullPath(relativePath: string): string {
		return path.join(this.baseUploadPath, relativePath);
	}

	/**
	 * Verifica se arquivo existe
	 */
	private async fileExists(filePath: string): Promise<boolean> {
		try {
			await fs.access(filePath);
			return true;
		} catch {
			return false;
		}
	}

	/**
	 * Remove arquivo do sistema
	 */
	private async deleteFile(filePath: string): Promise<void> {
		try {
			await fs.remove(filePath);
		} catch (error) {
			// Ignorar erro se arquivo não existir
			if ((error as any).code !== "ENOENT") {
				throw error;
			}
		}
	}
}
